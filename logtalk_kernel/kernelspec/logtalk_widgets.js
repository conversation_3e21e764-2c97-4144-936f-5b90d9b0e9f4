/**
 * Logtalk Jupyter Kernel Widget Support
 * JavaScript communication layer for HTML/JavaScript widgets
 */

// Global widget state management
window.LogtalkWidgets = {
    widgets: new Map(),
    kernel: null,
    
    // Initialize widget system
    init: function() {
        // Get reference to Jupyter kernel
        if (typeof Jupyter !== 'undefined' && Jupyter.notebook) {
            this.kernel = Jupyter.notebook.kernel;
        } else if (typeof window.parent !== 'undefined' && window.parent.Jupyter) {
            this.kernel = window.parent.Jupyter.notebook.kernel;
        }
        
        console.log('Logtalk Widgets initialized');
    },
    
    // Register a widget
    registerWidget: function(widgetId, type, initialValue) {
        this.widgets.set(widgetId, {
            type: type,
            value: initialValue,
            element: document.getElementById(widgetId)
        });
    },
    
    // Update widget value and notify kernel
    updateWidget: function(widgetId, value) {
        if (this.widgets.has(widgetId)) {
            const widget = this.widgets.get(widgetId);
            widget.value = value;
            
            // Send update to kernel
            this.sendWidgetUpdate(widgetId, value);
        }
    },
    
    // Send widget update to Logtalk kernel
    sendWidgetUpdate: function(widgetId, value) {
        if (!this.kernel) {
            console.warn('Kernel not available for widget update');
            return;
        }

        // Properly escape the value for Logtalk
        let escapedValue;
        if (typeof value === 'string') {
            // Escape single quotes and backslashes for Logtalk string literals
            escapedValue = value.replace(/\\/g, '\\\\').replace(/'/g, "\\'");
            escapedValue = `'${escapedValue}'`;
        } else if (typeof value === 'boolean') {
            escapedValue = value ? 'true' : 'false';
        } else {
            escapedValue = String(value);
        }

        const code = `jupyter_widget_handling::set_widget_value('${widgetId}', ${escapedValue}).`;

        console.log('Sending widget update:', code);

        this.kernel.execute(code, {
            silent: true,
            store_history: false,
            user_expressions: {},
            allow_stdin: false,
            stop_on_error: false
        });
    },
    
    // Get widget value
    getWidgetValue: function(widgetId) {
        if (this.widgets.has(widgetId)) {
            return this.widgets.get(widgetId).value;
        }
        return null;
    },
    
    // Set widget value from kernel
    setWidgetValue: function(widgetId, value) {
        if (this.widgets.has(widgetId)) {
            const widget = this.widgets.get(widgetId);
            widget.value = value;
            
            // Update DOM element
            const element = widget.element;
            if (element) {
                switch (widget.type) {
                    case 'text_input':
                    case 'number_input':
                        element.value = value;
                        break;
                    case 'slider':
                        element.value = value;
                        // Update display value
                        const valueDisplay = document.getElementById(widgetId + '_value');
                        if (valueDisplay) {
                            valueDisplay.textContent = value;
                        }
                        break;
                    case 'dropdown':
                        element.value = value;
                        break;
                    case 'checkbox':
                        element.checked = (value === true || value === 'true');
                        break;
                }
            }
        }
    },
    
    // Remove widget
    removeWidget: function(widgetId) {
        if (this.widgets.has(widgetId)) {
            this.widgets.delete(widgetId);
            
            // Remove DOM element
            const container = document.getElementById('container_' + widgetId);
            if (container) {
                container.remove();
            }
        }
    },
    
    // Clear all widgets
    clearAllWidgets: function() {
        this.widgets.forEach((widget, widgetId) => {
            this.removeWidget(widgetId);
        });
        this.widgets.clear();
    },

    // Debug function to check widget state
    debugWidgets: function() {
        console.log('Registered widgets:', this.widgets);
        console.log('Kernel available:', !!this.kernel);
        this.widgets.forEach((widget, widgetId) => {
            console.log(`Widget ${widgetId}:`, {
                type: widget.type,
                value: widget.value,
                element: widget.element,
                elementValue: widget.element ? widget.element.value : 'N/A'
            });
        });
    }
};

// Global function for widget updates (called from HTML)
function updateLogtalkWidget(widgetId, value) {
    LogtalkWidgets.updateWidget(widgetId, value);
}

// Auto-register widgets when they are created
function autoRegisterWidget(widgetId, type, initialValue) {
    LogtalkWidgets.registerWidget(widgetId, type, initialValue);
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        LogtalkWidgets.init();
    });
} else {
    LogtalkWidgets.init();
}

// Initialize when Jupyter is ready (for notebook environment)
if (typeof Jupyter !== 'undefined') {
    Jupyter.notebook.events.on('kernel_ready.Kernel', function() {
        LogtalkWidgets.init();
    });
}

// CSS styles for widgets
const widgetStyles = `
<style>
.logtalk-widget {
    margin: 10px 0;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    background-color: #fafafa;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.logtalk-widget label {
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
    display: inline-block;
}

.logtalk-widget input[type="text"],
.logtalk-widget input[type="number"],
.logtalk-widget select {
    width: 200px;
    padding: 6px 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.logtalk-widget input[type="text"]:focus,
.logtalk-widget input[type="number"]:focus,
.logtalk-widget select:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2);
}

.logtalk-widget input[type="range"] {
    width: 200px;
    margin: 5px 0;
}

.logtalk-widget input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.2);
}

.logtalk-widget button {
    background-color: #007cba;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.logtalk-widget button:hover {
    background-color: #005a87;
}

.logtalk-widget button:active {
    background-color: #004a73;
}
</style>
`;

// Inject CSS styles
if (document.head) {
    document.head.insertAdjacentHTML('beforeend', widgetStyles);
}
