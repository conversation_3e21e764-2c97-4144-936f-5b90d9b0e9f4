/**
 * Logtalk Jupyter Kernel Widget Support
 * JavaScript communication layer for HTML/JavaScript widgets
 */

// Global widget state management
window.LogtalkWidgets = {
    widgets: new Map(),
    kernel: null,
    
    // Initialize widget system
    init: function() {
        // Get reference to Jupyter kernel with multiple fallback methods
        this.kernel = null;

        // Method 1: JupyterLab API
        if (typeof window.jupyterapp !== 'undefined' && window.jupyterapp.shell) {
            try {
                const currentWidget = window.jupyterapp.shell.currentWidget;
                console.log('JupyterLab currentWidget:', currentWidget);
                if (currentWidget && currentWidget.context && currentWidget.context.sessionContext) {
                    console.log('JupyterLab sessionContext:', currentWidget.context.sessionContext);
                    const session = currentWidget.context.sessionContext.session;
                    console.log('JupyterLab session:', session);
                    if (session && session.kernel) {
                        this.kernel = session.kernel;
                        console.log('Kernel found via JupyterLab API:', session.kernel);
                    } else {
                        console.log('JupyterLab session or kernel not available');
                    }
                } else {
                    console.log('JupyterLab context or sessionContext not available');
                }
            } catch (e) {
                console.log('JupyterLab API method failed:', e);
            }
        } else {
            console.log('JupyterLab app not available');
        }

        // Method 2: Direct Jupyter reference (Classic Notebook)
        if (!this.kernel && typeof Jupyter !== 'undefined' && Jupyter.notebook && Jupyter.notebook.kernel) {
            this.kernel = Jupyter.notebook.kernel;
            console.log('Kernel found via Jupyter.notebook.kernel');
        }

        // Method 3: Try to find JupyterLab kernel through document
        if (!this.kernel) {
            try {
                // Look for JupyterLab kernel in the document's scripts
                const scripts = document.querySelectorAll('script');
                for (let script of scripts) {
                    if (script.textContent && script.textContent.includes('jupyter-widgets')) {
                        // Try to access JupyterLab's kernel manager
                        if (window.require) {
                            window.require(['@jupyterlab/services'], function(services) {
                                console.log('JupyterLab services available:', services);
                            });
                        }
                        break;
                    }
                }
            } catch (e) {
                console.log('JupyterLab document search failed:', e);
            }
        }

        // Method 4: Parent window Jupyter
        if (!this.kernel && typeof window.parent !== 'undefined' && window.parent.Jupyter &&
                 window.parent.Jupyter.notebook && window.parent.Jupyter.notebook.kernel) {
            this.kernel = window.parent.Jupyter.notebook.kernel;
            console.log('Kernel found via parent window');
        }

        // Method 5: IPython fallback
        if (!this.kernel && typeof window.IPython !== 'undefined' && window.IPython.notebook &&
                 window.IPython.notebook.kernel) {
            this.kernel = window.IPython.notebook.kernel;
            console.log('Kernel found via IPython.notebook.kernel');
        }

        // Method 6: Wait and retry
        if (!this.kernel) {
            console.log('Kernel not immediately available, will retry...');
            setTimeout(() => this.init(), 500);
            return;
        }

        console.log('Logtalk Widgets initialized with kernel:', !!this.kernel);
    },
    
    // Register a widget
    registerWidget: function(widgetId, type, initialValue) {
        this.widgets.set(widgetId, {
            type: type,
            value: initialValue,
            element: document.getElementById(widgetId)
        });
    },
    
    // Update widget value and notify kernel
    updateWidget: function(widgetId, value) {
        if (this.widgets.has(widgetId)) {
            const widget = this.widgets.get(widgetId);
            widget.value = value;
            
            // Send update to kernel
            this.sendWidgetUpdate(widgetId, value);
        }
    },
    
    // Send widget update to Logtalk kernel
    sendWidgetUpdate: function(widgetId, value) {
        // Properly escape the value for Logtalk
        let escapedValue;
        if (typeof value === 'string') {
            // Escape single quotes and backslashes for Logtalk string literals
            escapedValue = value.replace(/\\/g, '\\\\').replace(/'/g, "\\'");
            escapedValue = `'${escapedValue}'`;
        } else if (typeof value === 'boolean') {
            escapedValue = value ? 'true' : 'false';
        } else {
            escapedValue = String(value);
        }

        const code = `jupyter_widget_handling::set_widget_value('${widgetId}', ${escapedValue}).`;

        console.log('Attempting to send widget update:', code);

        // Try multiple execution methods in order of preference
        const executionMethods = [
            // Method 1: Use stored kernel reference
            () => {
                if (this.kernel) {
                    this.kernel.execute(code, {
                        silent: true,
                        store_history: false,
                        user_expressions: {},
                        allow_stdin: false,
                        stop_on_error: false
                    });
                    return true;
                }
                return false;
            },

            // Method 2: Direct Jupyter access
            () => {
                if (typeof Jupyter !== 'undefined' && Jupyter.notebook && Jupyter.notebook.kernel) {
                    Jupyter.notebook.kernel.execute(code, {
                        silent: true,
                        store_history: false,
                        user_expressions: {},
                        allow_stdin: false,
                        stop_on_error: false
                    });
                    this.kernel = Jupyter.notebook.kernel; // Store for future use
                    return true;
                }
                return false;
            },

            // Method 3: Try parent window
            () => {
                if (typeof window.parent !== 'undefined' &&
                    window.parent.Jupyter &&
                    window.parent.Jupyter.notebook &&
                    window.parent.Jupyter.notebook.kernel) {
                    window.parent.Jupyter.notebook.kernel.execute(code, {
                        silent: true,
                        store_history: false,
                        user_expressions: {},
                        allow_stdin: false,
                        stop_on_error: false
                    });
                    this.kernel = window.parent.Jupyter.notebook.kernel;
                    return true;
                }
                return false;
            },

            // Method 4: Try IPython fallback
            () => {
                if (typeof IPython !== 'undefined' && IPython.notebook && IPython.notebook.kernel) {
                    IPython.notebook.kernel.execute(code, {
                        silent: true,
                        store_history: false,
                        user_expressions: {},
                        allow_stdin: false,
                        stop_on_error: false
                    });
                    this.kernel = IPython.notebook.kernel;
                    return true;
                }
                return false;
            }
        ];

        // Try each method until one succeeds
        for (let i = 0; i < executionMethods.length; i++) {
            try {
                if (executionMethods[i]()) {
                    console.log(`Widget update sent successfully via method ${i + 1}`);
                    return;
                }
            } catch (error) {
                console.log(`Execution method ${i + 1} failed:`, error);
            }
        }

        // If all methods fail, log error and store value locally
        console.error('All kernel execution methods failed. Storing value locally.');
        if (this.widgets.has(widgetId)) {
            this.widgets.get(widgetId).value = value;
            console.log(`Value stored locally for widget ${widgetId}:`, value);
        }
    },
    
    // Get widget value
    getWidgetValue: function(widgetId) {
        if (this.widgets.has(widgetId)) {
            return this.widgets.get(widgetId).value;
        }
        return null;
    },
    
    // Set widget value from kernel
    setWidgetValue: function(widgetId, value) {
        if (this.widgets.has(widgetId)) {
            const widget = this.widgets.get(widgetId);
            widget.value = value;
            
            // Update DOM element
            const element = widget.element;
            if (element) {
                switch (widget.type) {
                    case 'text_input':
                    case 'number_input':
                        element.value = value;
                        break;
                    case 'slider':
                        element.value = value;
                        // Update display value
                        const valueDisplay = document.getElementById(widgetId + '_value');
                        if (valueDisplay) {
                            valueDisplay.textContent = value;
                        }
                        break;
                    case 'dropdown':
                        element.value = value;
                        break;
                    case 'checkbox':
                        element.checked = (value === true || value === 'true');
                        break;
                }
            }
        }
    },
    
    // Remove widget
    removeWidget: function(widgetId) {
        if (this.widgets.has(widgetId)) {
            this.widgets.delete(widgetId);
            
            // Remove DOM element
            const container = document.getElementById('container_' + widgetId);
            if (container) {
                container.remove();
            }
        }
    },
    
    // Clear all widgets
    clearAllWidgets: function() {
        this.widgets.forEach((widget, widgetId) => {
            this.removeWidget(widgetId);
        });
        this.widgets.clear();
    },

    // Debug function to check widget state
    debugWidgets: function() {
        console.log('Registered widgets:', this.widgets);
        console.log('Kernel available:', !!this.kernel);
        this.widgets.forEach((widget, widgetId) => {
            console.log(`Widget ${widgetId}:`, {
                type: widget.type,
                value: widget.value,
                element: widget.element,
                elementValue: widget.element ? widget.element.value : 'N/A'
            });
        });
    }
};

// Universal kernel execution function
function executeInKernel(code, options = {}) {
    console.log('Attempting to execute in kernel:', code);

    const defaultOptions = {
        silent: true,
        store_history: false,
        user_expressions: {},
        allow_stdin: false,
        stop_on_error: false,
        ...options
    };

    // Try multiple kernel access methods
    const kernelMethods = [
        // Method 1: Standard Jupyter notebook
        () => {
            if (typeof Jupyter !== 'undefined' && Jupyter.notebook && Jupyter.notebook.kernel) {
                Jupyter.notebook.kernel.execute(code, defaultOptions);
                return 'Jupyter.notebook.kernel';
            }
            return null;
        },

        // Method 2: IPython fallback
        () => {
            if (typeof IPython !== 'undefined' && IPython.notebook && IPython.notebook.kernel) {
                IPython.notebook.kernel.execute(code, defaultOptions);
                return 'IPython.notebook.kernel';
            }
            return null;
        },

        // Method 3: Parent window access
        () => {
            if (window.parent && window.parent.Jupyter && window.parent.Jupyter.notebook && window.parent.Jupyter.notebook.kernel) {
                window.parent.Jupyter.notebook.kernel.execute(code, defaultOptions);
                return 'window.parent.Jupyter.notebook.kernel';
            }
            return null;
        },

        // Method 4: Top window access
        () => {
            if (window.top && window.top.Jupyter && window.top.Jupyter.notebook && window.top.Jupyter.notebook.kernel) {
                window.top.Jupyter.notebook.kernel.execute(code, defaultOptions);
                return 'window.top.Jupyter.notebook.kernel';
            }
            return null;
        },

        // Method 5: VS Code notebook API
        () => {
            if (typeof acquireVsCodeApi !== 'undefined') {
                try {
                    const vscode = acquireVsCodeApi();
                    // VS Code has a different API - we'll need to post a message
                    vscode.postMessage({
                        type: 'executeCode',
                        code: code
                    });
                    return 'VS Code API';
                } catch (e) {
                    console.log('VS Code API failed:', e);
                }
            }
            return null;
        },

        // Method 6: JupyterLab context
        () => {
            console.log('Trying JupyterLab execution method...');
            // Try to find JupyterLab kernel through various paths
            const labPaths = [
                'window.jupyterapp',
                'window.parent.jupyterapp',
                'window.top.jupyterapp'
            ];

            for (const path of labPaths) {
                try {
                    console.log(`Trying JupyterLab path: ${path}`);
                    const app = eval(path);
                    console.log(`App found at ${path}:`, app);
                    if (app && app.shell && app.shell.currentWidget) {
                        const widget = app.shell.currentWidget;
                        console.log(`Current widget:`, widget);
                        if (widget.context && widget.context.sessionContext && widget.context.sessionContext.session) {
                            const kernel = widget.context.sessionContext.session.kernel;
                            console.log(`Kernel found:`, kernel);
                            if (kernel && kernel.requestExecute) {
                                console.log(`Executing code via JupyterLab: ${code}`);
                                const future = kernel.requestExecute({ code: code });
                                console.log('JupyterLab execution future:', future);
                                return `JupyterLab via ${path}`;
                            } else {
                                console.log('Kernel or requestExecute not available');
                            }
                        } else {
                            console.log('Context, sessionContext, or session not available');
                        }
                    } else {
                        console.log('App, shell, or currentWidget not available');
                    }
                } catch (e) {
                    console.log(`JupyterLab method ${path} failed:`, e);
                }
            }
            console.log('All JupyterLab paths failed');
            return null;
        },

        // Method 7: JupyterLab direct kernel access
        () => {
            if (typeof window.jupyterapp !== 'undefined') {
                try {
                    const app = window.jupyterapp;
                    const currentWidget = app.shell.currentWidget;
                    if (currentWidget && currentWidget.context) {
                        const sessionContext = currentWidget.context.sessionContext;
                        if (sessionContext && sessionContext.session && sessionContext.session.kernel) {
                            const kernel = sessionContext.session.kernel;
                            const future = kernel.requestExecute({ code: code });
                            console.log('JupyterLab direct execution:', future);
                            return 'JupyterLab direct';
                        }
                    }
                } catch (e) {
                    console.log('JupyterLab direct method failed:', e);
                }
            }
            return null;
        }
    ];

    // Try each method until one succeeds
    for (let i = 0; i < kernelMethods.length; i++) {
        try {
            const result = kernelMethods[i]();
            if (result) {
                console.log(`✅ Code executed successfully via: ${result}`);
                return true;
            }
        } catch (error) {
            console.log(`❌ Kernel method ${i + 1} failed:`, error);
        }
    }

    console.error('❌ All kernel execution methods failed');
    return false;
}

// Global function for widget updates (called from HTML)
function updateLogtalkWidget(widgetId, value) {
    console.log('updateLogtalkWidget called:', widgetId, value);

    // Properly escape the value for Logtalk
    let escapedValue;
    if (typeof value === 'string') {
        escapedValue = `'${value.replace(/\\/g, '\\\\').replace(/'/g, "\\'")}'`;
    } else if (typeof value === 'boolean') {
        escapedValue = value ? 'true' : 'false';
    } else {
        escapedValue = String(value);
    }

    const code = `jupyter_widget_handling::set_widget_value('${widgetId}', ${escapedValue}).`;
    console.log('Attempting to execute:', code);

    // Method 0: Try Jupyter Comm system (most reliable for modern JupyterLab)
    let executed = false;

    try {
        console.log('Trying Jupyter Comm approach...');

        // Look for Jupyter comm manager in various locations
        let commManager = null;

        // Check for comm manager in common locations
        const commPaths = [
            'window.Jupyter.notebook.kernel.comm_manager',
            'window.IPython.notebook.kernel.comm_manager',
            'window.parent.Jupyter.notebook.kernel.comm_manager'
        ];

        for (const path of commPaths) {
            try {
                commManager = eval(path);
                if (commManager) {
                    console.log(`Found comm manager at: ${path}`);
                    break;
                }
            } catch (e) {
                // Continue searching
            }
        }

        // If no direct comm manager, try to create a custom message
        if (!commManager) {
            console.log('No comm manager found, trying custom message approach');

            // Try to send a message through any available channel
            const message = {
                header: {
                    msg_id: 'widget-' + Date.now(),
                    msg_type: 'execute_request'
                },
                content: {
                    code: code,
                    silent: true,
                    store_history: false
                }
            };

            // Try to find any message sending mechanism
            if (window.postMessage) {
                window.postMessage(message, '*');
                console.log('Sent via window.postMessage');
                executed = true;
            }

            // Try parent window
            if (window.parent && window.parent.postMessage && window.parent !== window) {
                window.parent.postMessage(message, '*');
                console.log('Sent via parent.postMessage');
                executed = true;
            }
        }

    } catch (e) {
        console.log('Comm approach failed:', e);
    }

    // Modern JupyterLab kernel detection
    let executed = false;

    // Method 1: Try to find JupyterLab application instance
    try {
        console.log('Searching for JupyterLab application instance...');

        // Look for the actual application in various places
        let app = null;

        // Check for common JupyterLab app locations
        const appLocations = [
            'window.jupyterapp',
            'window.application',
            'window.lab',
            'window.jupyterlab',
            'window.app'
        ];

        for (const location of appLocations) {
            try {
                const obj = eval(location);
                if (obj && obj.shell && obj.shell.currentWidget) {
                    app = obj;
                    console.log(`Found app at ${location}:`, app);
                    break;
                }
            } catch (e) {
                // Continue searching
            }
        }

        // If not found in globals, try to find through DOM
        if (!app) {
            console.log('Searching for app through DOM...');
            // Look for elements that might contain the app reference
            const bodyElement = document.body;
            if (bodyElement && bodyElement._jupyterlab) {
                app = bodyElement._jupyterlab;
                console.log('Found app in document.body._jupyterlab:', app);
            }
        }

        // Try to find through webpack chunks
        if (!app && typeof window.webpackChunk_jupyterlab_application_top !== 'undefined') {
            console.log('Searching through webpack chunks...');
            try {
                // This is complex but sometimes the app is stored in webpack modules
                const chunks = window.webpackChunk_jupyterlab_application_top;
                if (Array.isArray(chunks)) {
                    for (const chunk of chunks) {
                        if (chunk && chunk[1]) {
                            const modules = chunk[1];
                            for (const moduleId in modules) {
                                const module = modules[moduleId];
                                if (typeof module === 'function') {
                                    try {
                                        // This is a hack but might work
                                        const moduleStr = module.toString();
                                        if (moduleStr.includes('shell') && moduleStr.includes('currentWidget')) {
                                            console.log('Found potential app module:', moduleId);
                                        }
                                    } catch (e) {
                                        // Continue
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (e) {
                console.log('Webpack search failed:', e);
            }
        }

        // If we found an app, try to use it
        if (app && app.shell && app.shell.currentWidget) {
            const widget = app.shell.currentWidget;
            console.log('Current widget found:', widget);

            if (widget.context && widget.context.sessionContext && widget.context.sessionContext.session) {
                const kernel = widget.context.sessionContext.session.kernel;
                console.log('Kernel found:', kernel);

                if (kernel && kernel.requestExecute) {
                    console.log('✅ Using JupyterLab application kernel');
                    kernel.requestExecute({ code: code });
                    executed = true;
                }
            }
        }

    } catch (e) {
        console.log('JupyterLab application search failed:', e);
    }

    // Method 2: Try webpack chunk approach
    if (!executed && typeof window.webpackChunk_jupyterlab_application_top !== 'undefined') {
        try {
            console.log('Trying webpack chunk approach');
            const chunks = window.webpackChunk_jupyterlab_application_top;
            // This is complex and would need more investigation
            console.log('Webpack chunks available but need specific module access');
        } catch (e) {
            console.log('Webpack chunk approach failed:', e);
        }
    }

    // Method 3: Try to find kernel through global search
    if (!executed) {
        try {
            console.log('Trying global kernel search');
            const globalKeys = Object.keys(window);
            for (let key of globalKeys) {
                if (key.toLowerCase().includes('jupyter') || key.toLowerCase().includes('kernel')) {
                    const obj = window[key];
                    if (obj && typeof obj === 'object') {
                        // Try to find execute method
                        if (obj.execute || obj.requestExecute) {
                            console.log('Found execute method in', key);
                            try {
                                if (obj.execute) {
                                    obj.execute(code, { silent: true });
                                } else {
                                    obj.requestExecute({ code: code });
                                }
                                executed = true;
                                console.log('✅ Executed via global object:', key);
                                break;
                            } catch (e) {
                                console.log('Failed to execute via', key, ':', e);
                            }
                        }
                    }
                }
            }
        } catch (e) {
            console.log('Global kernel search failed:', e);
        }
    }

    // Method 2: Try postMessage to parent (for iframe scenarios)
    if (!executed) {
        try {
            console.log('Trying postMessage approach');
            const message = {
                type: 'jupyter_execute',
                code: code,
                widgetId: widgetId,
                value: value
            };

            if (window.parent && window.parent !== window) {
                window.parent.postMessage(message, '*');
                console.log('Posted message to parent window');
                executed = true; // Assume it worked
            } else if (window.top && window.top !== window) {
                window.top.postMessage(message, '*');
                console.log('Posted message to top window');
                executed = true; // Assume it worked
            }
        } catch (e) {
            console.log('PostMessage approach failed:', e);
        }
    }

    // Method 3: Try custom event dispatch
    if (!executed) {
        try {
            console.log('Trying custom event approach');
            const event = new CustomEvent('jupyter_widget_update', {
                detail: {
                    code: code,
                    widgetId: widgetId,
                    value: value
                }
            });
            document.dispatchEvent(event);
            window.dispatchEvent(event);
            if (window.parent) {
                window.parent.document.dispatchEvent(event);
            }
            console.log('Dispatched custom event');
            executed = true; // Assume it worked
        } catch (e) {
            console.log('Custom event approach failed:', e);
        }
    }

    // Method: Try WebSocket approach (for direct kernel communication)
    if (!executed) {
        try {
            console.log('Trying WebSocket approach...');

            // Look for existing WebSocket connections
            if (window.WebSocket) {
                // This is a more advanced approach that would require knowing the kernel WebSocket URL
                console.log('WebSocket available but need kernel URL');

                // Try to find kernel info in the page
                const scripts = document.querySelectorAll('script');
                for (const script of scripts) {
                    if (script.textContent && script.textContent.includes('ws://') || script.textContent.includes('wss://')) {
                        console.log('Found potential WebSocket URL in script');
                        // This would need more sophisticated parsing
                        break;
                    }
                }
            }
        } catch (e) {
            console.log('WebSocket approach failed:', e);
        }
    }

    // Method: Try fetch to kernel API
    if (!executed) {
        try {
            console.log('Trying fetch to kernel API...');

            // Try to determine the kernel API endpoint
            const baseUrl = window.location.origin;
            const possiblePaths = [
                '/api/kernels',
                '/jupyter/api/kernels',
                '/lab/api/kernels'
            ];

            // This would be async, but we'll try a simple approach
            console.log('Kernel API endpoints to try:', possiblePaths.map(p => baseUrl + p));

            // For now, just log that this approach is available
            console.log('Fetch approach available but needs async implementation');

        } catch (e) {
            console.log('Fetch approach failed:', e);
        }
    }

    if (executed) {
        console.log('✅ Widget update sent via communication protocol');
        // Update local state
        if (typeof LogtalkWidgets !== 'undefined') {
            LogtalkWidgets.updateWidget(widgetId, value);
        }
    } else {
        console.error('❌ All communication methods failed');
        console.log('Storing value locally and trying alternative approaches...');

        // Store locally as fallback
        if (typeof LogtalkWidgets !== 'undefined') {
            LogtalkWidgets.updateWidget(widgetId, value);
        }

        // Try a simple HTTP request as last resort
        try {
            console.log('Last resort: trying simple HTTP request...');

            // Create a simple GET request that might trigger something
            const img = new Image();
            img.src = `/jupyter/execute?code=${encodeURIComponent(code)}&t=${Date.now()}`;
            img.onerror = () => console.log('HTTP request failed (expected)');
            img.onload = () => console.log('HTTP request succeeded (unexpected)');

            console.log('HTTP request attempt made');

        } catch (e) {
            console.log('HTTP request failed:', e);
        }

        // Final fallback: store in localStorage for manual retrieval
        try {
            const widgetData = {
                widgetId: widgetId,
                value: value,
                code: code,
                timestamp: Date.now()
            };
            localStorage.setItem('logtalk_widget_' + widgetId, JSON.stringify(widgetData));
            console.log('Stored in localStorage for manual retrieval:', widgetData);
        } catch (e) {
            console.log('localStorage storage failed:', e);
        }
    }
}

// Auto-register widgets when they are created
function autoRegisterWidget(widgetId, type, initialValue) {
    LogtalkWidgets.registerWidget(widgetId, type, initialValue);
}

// Manual kernel connection function for debugging
function connectLogtalkWidgetsToKernel() {
    console.log('Attempting manual kernel connection...');

    // Try all possible kernel references
    const kernelSources = [
        () => Jupyter && Jupyter.notebook && Jupyter.notebook.kernel,
        () => IPython && IPython.notebook && IPython.notebook.kernel,
        () => window.parent.Jupyter && window.parent.Jupyter.notebook && window.parent.Jupyter.notebook.kernel,
        () => window.parent.IPython && window.parent.IPython.notebook && window.parent.IPython.notebook.kernel
    ];

    for (let i = 0; i < kernelSources.length; i++) {
        try {
            const kernel = kernelSources[i]();
            if (kernel) {
                LogtalkWidgets.kernel = kernel;
                console.log(`Kernel connected via method ${i + 1}:`, kernel);
                return true;
            }
        } catch (e) {
            console.log(`Kernel connection method ${i + 1} failed:`, e);
        }
    }

    console.log('All kernel connection methods failed');
    return false;
}

// Make functions globally available for debugging
window.connectLogtalkWidgetsToKernel = connectLogtalkWidgetsToKernel;
window.LogtalkWidgets = LogtalkWidgets;

// Multiple initialization strategies
function initializeWidgets() {
    LogtalkWidgets.init();

    // Keep trying until kernel is available
    if (!LogtalkWidgets.kernel) {
        setTimeout(initializeWidgets, 1000);
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeWidgets);
} else {
    initializeWidgets();
}

// Initialize when Jupyter is ready (for notebook environment)
if (typeof Jupyter !== 'undefined') {
    // Try multiple Jupyter events
    if (Jupyter.notebook && Jupyter.notebook.events) {
        Jupyter.notebook.events.on('kernel_ready.Kernel', function() {
            console.log('Kernel ready event fired');
            LogtalkWidgets.init();
        });

        Jupyter.notebook.events.on('kernel_connected.Kernel', function() {
            console.log('Kernel connected event fired');
            LogtalkWidgets.init();
        });
    }
}

// Fallback: Keep trying to initialize
setTimeout(function() {
    if (!LogtalkWidgets.kernel) {
        console.log('Fallback initialization attempt');
        LogtalkWidgets.init();
    }
}, 2000);

// CSS styles for widgets
const widgetStyles = `
<style>
.logtalk-widget {
    margin: 10px 0;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    background-color: #fafafa;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.logtalk-widget label {
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
    display: inline-block;
}

.logtalk-widget input[type="text"],
.logtalk-widget input[type="number"],
.logtalk-widget select {
    width: 200px;
    padding: 6px 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.logtalk-widget input[type="text"]:focus,
.logtalk-widget input[type="number"]:focus,
.logtalk-widget select:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2);
}

.logtalk-widget input[type="range"] {
    width: 200px;
    margin: 5px 0;
}

.logtalk-widget input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.2);
}

.logtalk-widget button {
    background-color: #007cba;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.logtalk-widget button:hover {
    background-color: #005a87;
}

.logtalk-widget button:active {
    background-color: #004a73;
}
</style>
`;

// Inject CSS styles
if (document.head) {
    document.head.insertAdjacentHTML('beforeend', widgetStyles);
}
