/**
 * Logtalk Jupyter Kernel Widget Support
 * JavaScript communication layer for HTML/JavaScript widgets
 */

// Global widget state management
window.LogtalkWidgets = {
    widgets: new Map(),
    kernel: null,
    
    // Initialize widget system
    init: function() {
        // Get reference to Jupyter kernel with multiple fallback methods
        this.kernel = null;

        // Method 1: JupyterLab API
        if (typeof window.jupyterapp !== 'undefined' && window.jupyterapp.shell) {
            try {
                const currentWidget = window.jupyterapp.shell.currentWidget;
                if (currentWidget && currentWidget.context && currentWidget.context.sessionContext) {
                    const session = currentWidget.context.sessionContext.session;
                    if (session && session.kernel) {
                        this.kernel = session.kernel;
                        console.log('Kernel found via JupyterLab API');
                    }
                }
            } catch (e) {
                console.log('JupyterLab API method failed:', e);
            }
        }

        // Method 2: Direct Jupyter reference (Classic Notebook)
        if (!this.kernel && typeof Jupyter !== 'undefined' && Jupyter.notebook && Jupyter.notebook.kernel) {
            this.kernel = Jupyter.notebook.kernel;
            console.log('Kernel found via Jupyter.notebook.kernel');
        }

        // Method 3: Try to find JupyterLab kernel through document
        if (!this.kernel) {
            try {
                // Look for JupyterLab kernel in the document's scripts
                const scripts = document.querySelectorAll('script');
                for (let script of scripts) {
                    if (script.textContent && script.textContent.includes('jupyter-widgets')) {
                        // Try to access JupyterLab's kernel manager
                        if (window.require) {
                            window.require(['@jupyterlab/services'], function(services) {
                                console.log('JupyterLab services available:', services);
                            });
                        }
                        break;
                    }
                }
            } catch (e) {
                console.log('JupyterLab document search failed:', e);
            }
        }

        // Method 4: Parent window Jupyter
        if (!this.kernel && typeof window.parent !== 'undefined' && window.parent.Jupyter &&
                 window.parent.Jupyter.notebook && window.parent.Jupyter.notebook.kernel) {
            this.kernel = window.parent.Jupyter.notebook.kernel;
            console.log('Kernel found via parent window');
        }

        // Method 5: IPython fallback
        if (!this.kernel && typeof window.IPython !== 'undefined' && window.IPython.notebook &&
                 window.IPython.notebook.kernel) {
            this.kernel = window.IPython.notebook.kernel;
            console.log('Kernel found via IPython.notebook.kernel');
        }

        // Method 6: Wait and retry
        if (!this.kernel) {
            console.log('Kernel not immediately available, will retry...');
            setTimeout(() => this.init(), 500);
            return;
        }

        console.log('Logtalk Widgets initialized with kernel:', !!this.kernel);
    },
    
    // Register a widget
    registerWidget: function(widgetId, type, initialValue) {
        this.widgets.set(widgetId, {
            type: type,
            value: initialValue,
            element: document.getElementById(widgetId)
        });
    },
    
    // Update widget value and notify kernel
    updateWidget: function(widgetId, value) {
        if (this.widgets.has(widgetId)) {
            const widget = this.widgets.get(widgetId);
            widget.value = value;
            
            // Send update to kernel
            this.sendWidgetUpdate(widgetId, value);
        }
    },
    
    // Send widget update to Logtalk kernel
    sendWidgetUpdate: function(widgetId, value) {
        // Properly escape the value for Logtalk
        let escapedValue;
        if (typeof value === 'string') {
            // Escape single quotes and backslashes for Logtalk string literals
            escapedValue = value.replace(/\\/g, '\\\\').replace(/'/g, "\\'");
            escapedValue = `'${escapedValue}'`;
        } else if (typeof value === 'boolean') {
            escapedValue = value ? 'true' : 'false';
        } else {
            escapedValue = String(value);
        }

        const code = `jupyter_widget_handling::set_widget_value('${widgetId}', ${escapedValue}).`;

        console.log('Attempting to send widget update:', code);

        // Try multiple execution methods in order of preference
        const executionMethods = [
            // Method 1: Use stored kernel reference
            () => {
                if (this.kernel) {
                    this.kernel.execute(code, {
                        silent: true,
                        store_history: false,
                        user_expressions: {},
                        allow_stdin: false,
                        stop_on_error: false
                    });
                    return true;
                }
                return false;
            },

            // Method 2: Direct Jupyter access
            () => {
                if (typeof Jupyter !== 'undefined' && Jupyter.notebook && Jupyter.notebook.kernel) {
                    Jupyter.notebook.kernel.execute(code, {
                        silent: true,
                        store_history: false,
                        user_expressions: {},
                        allow_stdin: false,
                        stop_on_error: false
                    });
                    this.kernel = Jupyter.notebook.kernel; // Store for future use
                    return true;
                }
                return false;
            },

            // Method 3: Try parent window
            () => {
                if (typeof window.parent !== 'undefined' &&
                    window.parent.Jupyter &&
                    window.parent.Jupyter.notebook &&
                    window.parent.Jupyter.notebook.kernel) {
                    window.parent.Jupyter.notebook.kernel.execute(code, {
                        silent: true,
                        store_history: false,
                        user_expressions: {},
                        allow_stdin: false,
                        stop_on_error: false
                    });
                    this.kernel = window.parent.Jupyter.notebook.kernel;
                    return true;
                }
                return false;
            },

            // Method 4: Try IPython fallback
            () => {
                if (typeof IPython !== 'undefined' && IPython.notebook && IPython.notebook.kernel) {
                    IPython.notebook.kernel.execute(code, {
                        silent: true,
                        store_history: false,
                        user_expressions: {},
                        allow_stdin: false,
                        stop_on_error: false
                    });
                    this.kernel = IPython.notebook.kernel;
                    return true;
                }
                return false;
            }
        ];

        // Try each method until one succeeds
        for (let i = 0; i < executionMethods.length; i++) {
            try {
                if (executionMethods[i]()) {
                    console.log(`Widget update sent successfully via method ${i + 1}`);
                    return;
                }
            } catch (error) {
                console.log(`Execution method ${i + 1} failed:`, error);
            }
        }

        // If all methods fail, log error and store value locally
        console.error('All kernel execution methods failed. Storing value locally.');
        if (this.widgets.has(widgetId)) {
            this.widgets.get(widgetId).value = value;
            console.log(`Value stored locally for widget ${widgetId}:`, value);
        }
    },
    
    // Get widget value
    getWidgetValue: function(widgetId) {
        if (this.widgets.has(widgetId)) {
            return this.widgets.get(widgetId).value;
        }
        return null;
    },
    
    // Set widget value from kernel
    setWidgetValue: function(widgetId, value) {
        if (this.widgets.has(widgetId)) {
            const widget = this.widgets.get(widgetId);
            widget.value = value;
            
            // Update DOM element
            const element = widget.element;
            if (element) {
                switch (widget.type) {
                    case 'text_input':
                    case 'number_input':
                        element.value = value;
                        break;
                    case 'slider':
                        element.value = value;
                        // Update display value
                        const valueDisplay = document.getElementById(widgetId + '_value');
                        if (valueDisplay) {
                            valueDisplay.textContent = value;
                        }
                        break;
                    case 'dropdown':
                        element.value = value;
                        break;
                    case 'checkbox':
                        element.checked = (value === true || value === 'true');
                        break;
                }
            }
        }
    },
    
    // Remove widget
    removeWidget: function(widgetId) {
        if (this.widgets.has(widgetId)) {
            this.widgets.delete(widgetId);
            
            // Remove DOM element
            const container = document.getElementById('container_' + widgetId);
            if (container) {
                container.remove();
            }
        }
    },
    
    // Clear all widgets
    clearAllWidgets: function() {
        this.widgets.forEach((widget, widgetId) => {
            this.removeWidget(widgetId);
        });
        this.widgets.clear();
    },

    // Debug function to check widget state
    debugWidgets: function() {
        console.log('Registered widgets:', this.widgets);
        console.log('Kernel available:', !!this.kernel);
        this.widgets.forEach((widget, widgetId) => {
            console.log(`Widget ${widgetId}:`, {
                type: widget.type,
                value: widget.value,
                element: widget.element,
                elementValue: widget.element ? widget.element.value : 'N/A'
            });
        });
    }
};

// Universal kernel execution function
function executeInKernel(code, options = {}) {
    console.log('Attempting to execute in kernel:', code);

    const defaultOptions = {
        silent: true,
        store_history: false,
        user_expressions: {},
        allow_stdin: false,
        stop_on_error: false,
        ...options
    };

    // Try multiple kernel access methods
    const kernelMethods = [
        // Method 1: Standard Jupyter notebook
        () => {
            if (typeof Jupyter !== 'undefined' && Jupyter.notebook && Jupyter.notebook.kernel) {
                Jupyter.notebook.kernel.execute(code, defaultOptions);
                return 'Jupyter.notebook.kernel';
            }
            return null;
        },

        // Method 2: IPython fallback
        () => {
            if (typeof IPython !== 'undefined' && IPython.notebook && IPython.notebook.kernel) {
                IPython.notebook.kernel.execute(code, defaultOptions);
                return 'IPython.notebook.kernel';
            }
            return null;
        },

        // Method 3: Parent window access
        () => {
            if (window.parent && window.parent.Jupyter && window.parent.Jupyter.notebook && window.parent.Jupyter.notebook.kernel) {
                window.parent.Jupyter.notebook.kernel.execute(code, defaultOptions);
                return 'window.parent.Jupyter.notebook.kernel';
            }
            return null;
        },

        // Method 4: Top window access
        () => {
            if (window.top && window.top.Jupyter && window.top.Jupyter.notebook && window.top.Jupyter.notebook.kernel) {
                window.top.Jupyter.notebook.kernel.execute(code, defaultOptions);
                return 'window.top.Jupyter.notebook.kernel';
            }
            return null;
        },

        // Method 5: VS Code notebook API
        () => {
            if (typeof acquireVsCodeApi !== 'undefined') {
                try {
                    const vscode = acquireVsCodeApi();
                    // VS Code has a different API - we'll need to post a message
                    vscode.postMessage({
                        type: 'executeCode',
                        code: code
                    });
                    return 'VS Code API';
                } catch (e) {
                    console.log('VS Code API failed:', e);
                }
            }
            return null;
        },

        // Method 6: JupyterLab context
        () => {
            // Try to find JupyterLab kernel through various paths
            const labPaths = [
                'window.jupyterapp',
                'window.parent.jupyterapp',
                'window.top.jupyterapp'
            ];

            for (const path of labPaths) {
                try {
                    const app = eval(path);
                    if (app && app.shell && app.shell.currentWidget) {
                        const widget = app.shell.currentWidget;
                        if (widget.context && widget.context.sessionContext && widget.context.sessionContext.session) {
                            const kernel = widget.context.sessionContext.session.kernel;
                            if (kernel && kernel.requestExecute) {
                                const future = kernel.requestExecute({ code: code });
                                console.log('JupyterLab execution future:', future);
                                return `JupyterLab via ${path}`;
                            }
                        }
                    }
                } catch (e) {
                    console.log(`JupyterLab method ${path} failed:`, e);
                }
            }
            return null;
        },

        // Method 7: JupyterLab direct kernel access
        () => {
            if (typeof window.jupyterapp !== 'undefined') {
                try {
                    const app = window.jupyterapp;
                    const currentWidget = app.shell.currentWidget;
                    if (currentWidget && currentWidget.context) {
                        const sessionContext = currentWidget.context.sessionContext;
                        if (sessionContext && sessionContext.session && sessionContext.session.kernel) {
                            const kernel = sessionContext.session.kernel;
                            const future = kernel.requestExecute({ code: code });
                            console.log('JupyterLab direct execution:', future);
                            return 'JupyterLab direct';
                        }
                    }
                } catch (e) {
                    console.log('JupyterLab direct method failed:', e);
                }
            }
            return null;
        }
    ];

    // Try each method until one succeeds
    for (let i = 0; i < kernelMethods.length; i++) {
        try {
            const result = kernelMethods[i]();
            if (result) {
                console.log(`✅ Code executed successfully via: ${result}`);
                return true;
            }
        } catch (error) {
            console.log(`❌ Kernel method ${i + 1} failed:`, error);
        }
    }

    console.error('❌ All kernel execution methods failed');
    return false;
}

// Global function for widget updates (called from HTML)
function updateLogtalkWidget(widgetId, value) {
    console.log('updateLogtalkWidget called:', widgetId, value);

    // Properly escape the value for Logtalk
    let escapedValue;
    if (typeof value === 'string') {
        escapedValue = `'${value.replace(/\\/g, '\\\\').replace(/'/g, "\\'")}'`;
    } else if (typeof value === 'boolean') {
        escapedValue = value ? 'true' : 'false';
    } else {
        escapedValue = String(value);
    }

    const code = `jupyter_widget_handling::set_widget_value('${widgetId}', ${escapedValue}).`;

    // Try universal kernel execution
    const success = executeInKernel(code);

    if (success) {
        console.log('✅ Widget update sent successfully');

        // Also update local state if LogtalkWidgets is available
        if (typeof LogtalkWidgets !== 'undefined' && LogtalkWidgets.widgets && LogtalkWidgets.widgets.has(widgetId)) {
            LogtalkWidgets.widgets.get(widgetId).value = value;
        }
    } else {
        console.error('❌ Widget update failed - storing locally only');

        // Store locally as fallback
        if (typeof LogtalkWidgets !== 'undefined') {
            if (!LogtalkWidgets.widgets) {
                LogtalkWidgets.widgets = new Map();
            }
            if (!LogtalkWidgets.widgets.has(widgetId)) {
                LogtalkWidgets.widgets.set(widgetId, { type: 'unknown', value: value });
            } else {
                LogtalkWidgets.widgets.get(widgetId).value = value;
            }
            console.log('Value stored locally for widget:', widgetId, value);
        }
    }
}

// Auto-register widgets when they are created
function autoRegisterWidget(widgetId, type, initialValue) {
    LogtalkWidgets.registerWidget(widgetId, type, initialValue);
}

// Manual kernel connection function for debugging
function connectLogtalkWidgetsToKernel() {
    console.log('Attempting manual kernel connection...');

    // Try all possible kernel references
    const kernelSources = [
        () => Jupyter && Jupyter.notebook && Jupyter.notebook.kernel,
        () => IPython && IPython.notebook && IPython.notebook.kernel,
        () => window.parent.Jupyter && window.parent.Jupyter.notebook && window.parent.Jupyter.notebook.kernel,
        () => window.parent.IPython && window.parent.IPython.notebook && window.parent.IPython.notebook.kernel
    ];

    for (let i = 0; i < kernelSources.length; i++) {
        try {
            const kernel = kernelSources[i]();
            if (kernel) {
                LogtalkWidgets.kernel = kernel;
                console.log(`Kernel connected via method ${i + 1}:`, kernel);
                return true;
            }
        } catch (e) {
            console.log(`Kernel connection method ${i + 1} failed:`, e);
        }
    }

    console.log('All kernel connection methods failed');
    return false;
}

// Make functions globally available for debugging
window.connectLogtalkWidgetsToKernel = connectLogtalkWidgetsToKernel;
window.LogtalkWidgets = LogtalkWidgets;

// Multiple initialization strategies
function initializeWidgets() {
    LogtalkWidgets.init();

    // Keep trying until kernel is available
    if (!LogtalkWidgets.kernel) {
        setTimeout(initializeWidgets, 1000);
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeWidgets);
} else {
    initializeWidgets();
}

// Initialize when Jupyter is ready (for notebook environment)
if (typeof Jupyter !== 'undefined') {
    // Try multiple Jupyter events
    if (Jupyter.notebook && Jupyter.notebook.events) {
        Jupyter.notebook.events.on('kernel_ready.Kernel', function() {
            console.log('Kernel ready event fired');
            LogtalkWidgets.init();
        });

        Jupyter.notebook.events.on('kernel_connected.Kernel', function() {
            console.log('Kernel connected event fired');
            LogtalkWidgets.init();
        });
    }
}

// Fallback: Keep trying to initialize
setTimeout(function() {
    if (!LogtalkWidgets.kernel) {
        console.log('Fallback initialization attempt');
        LogtalkWidgets.init();
    }
}, 2000);

// CSS styles for widgets
const widgetStyles = `
<style>
.logtalk-widget {
    margin: 10px 0;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    background-color: #fafafa;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.logtalk-widget label {
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
    display: inline-block;
}

.logtalk-widget input[type="text"],
.logtalk-widget input[type="number"],
.logtalk-widget select {
    width: 200px;
    padding: 6px 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.logtalk-widget input[type="text"]:focus,
.logtalk-widget input[type="number"]:focus,
.logtalk-widget select:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2);
}

.logtalk-widget input[type="range"] {
    width: 200px;
    margin: 5px 0;
}

.logtalk-widget input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.2);
}

.logtalk-widget button {
    background-color: #007cba;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.logtalk-widget button:hover {
    background-color: #005a87;
}

.logtalk-widget button:active {
    background-color: #004a73;
}
</style>
`;

// Inject CSS styles
if (document.head) {
    document.head.insertAdjacentHTML('beforeend', widgetStyles);
}
