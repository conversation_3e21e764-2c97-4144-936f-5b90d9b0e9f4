%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%
%  This file is part of Logtalk <https://logtalk.org/>
%  SPDX-FileCopyrightText: 1998-2025 <PERSON> <<EMAIL>>
%  SPDX-License-Identifier: Apache-2.0
%
%  Licensed under the Apache License, Version 2.0 (the "License");
%  you may not use this file except in compliance with the License.
%  You may obtain a copy of the License at
%
%      http://www.apache.org/licenses/LICENSE-2.0
%
%  Unless required by applicable law or agreed to in writing, software
%  distributed under the License is distributed on an "AS IS" BASIS,
%  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
%  See the License for the specific language governing permissions and
%  limitations under the License.
%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%


% This object provides VS Code compatible widgets that work without JavaScript
% by using simple text-based input prompts and server-side state management.


:- object(jupyter_vscode_widgets).

	:- info([
		version is 0:1:0,
		author is 'Paulo Moura',
		date is 2025-01-27,
		comment is 'VS Code compatible widgets using text-based input prompts.'
	]).

	:- public([
		prompt_text_input/3,        % prompt_text_input(+WidgetId, +Prompt, +DefaultValue)
		prompt_number_input/3,      % prompt_number_input(+WidgetId, +Prompt, +DefaultValue)
		prompt_choice/3,            % prompt_choice(+WidgetId, +Prompt, +Options)
		prompt_yes_no/2,            % prompt_yes_no(+WidgetId, +Prompt)
		set_widget_value/2,         % set_widget_value(+WidgetId, +Value)
		get_widget_value/2,         % get_widget_value(+WidgetId, -Value)
		widget_exists/1,            % widget_exists(+WidgetId)
		list_widgets/0,             % list_widgets
		clear_widgets/0             % clear_widgets
	]).

	:- uses(jupyter_logging, [log/1, log/2]).
	:- uses(jupyter_response_handling, [assert_success_response/4]).

	% Dynamic predicate to store widget values
	:- dynamic(widget_value/2).  % widget_value(WidgetId, Value)

	% Prompt for text input
	prompt_text_input(WidgetId, Prompt, DefaultValue) :-
		format('~n=== TEXT INPUT ===~n', []),
		format('Widget ID: ~w~n', [WidgetId]),
		format('~w~n', [Prompt]),
		format('Default: ~w~n', [DefaultValue]),
		format('Enter your value (or press Enter for default): ', []),
		read_line_to_string(user_input, Input),
		(	Input == '' ->
			Value = DefaultValue
		;	Value = Input
		),
		retractall(widget_value(WidgetId, _)),
		assertz(widget_value(WidgetId, Value)),
		format('Value set: ~w~n', [Value]),
		format('==================~n~n', []).

	% Prompt for number input
	prompt_number_input(WidgetId, Prompt, DefaultValue) :-
		format('~n=== NUMBER INPUT ===~n', []),
		format('Widget ID: ~w~n', [WidgetId]),
		format('~w~n', [Prompt]),
		format('Default: ~w~n', [DefaultValue]),
		format('Enter a number (or press Enter for default): ', []),
		read_line_to_string(user_input, Input),
		(	Input == '' ->
			Value = DefaultValue
		;	catch(atom_number(Input, Value), _, (
				format('Invalid number, using default: ~w~n', [DefaultValue]),
				Value = DefaultValue
			))
		),
		retractall(widget_value(WidgetId, _)),
		assertz(widget_value(WidgetId, Value)),
		format('Value set: ~w~n', [Value]),
		format('===================~n~n', []).

	% Prompt for choice from options
	prompt_choice(WidgetId, Prompt, Options) :-
		format('~n=== CHOICE INPUT ===~n', []),
		format('Widget ID: ~w~n', [WidgetId]),
		format('~w~n', [Prompt]),
		format('Options:~n', []),
		display_options(Options, 1),
		length(Options, MaxOption),
		format('Enter option number (1-~w): ', [MaxOption]),
		read_line_to_string(user_input, Input),
		(	catch(atom_number(Input, OptionNum), _, fail),
			OptionNum >= 1,
			OptionNum =< MaxOption ->
			nth1(OptionNum, Options, Value)
		;	format('Invalid choice, using first option~n', []),
			Options = [Value|_]
		),
		retractall(widget_value(WidgetId, _)),
		assertz(widget_value(WidgetId, Value)),
		format('Value set: ~w~n', [Value]),
		format('==================~n~n', []).

	% Display numbered options
	display_options([], _).
	display_options([Option|Rest], N) :-
		format('  ~w. ~w~n', [N, Option]),
		N1 is N + 1,
		display_options(Rest, N1).

	% Prompt for yes/no
	prompt_yes_no(WidgetId, Prompt) :-
		format('~n=== YES/NO INPUT ===~n', []),
		format('Widget ID: ~w~n', [WidgetId]),
		format('~w~n', [Prompt]),
		format('Enter y/yes or n/no: ', []),
		read_line_to_string(user_input, Input),
		atom_string(InputAtom, Input),
		downcase_atom(InputAtom, LowerInput),
		(	memberchk(LowerInput, [y, yes, true, 1]) ->
			Value = true
		;	memberchk(LowerInput, [n, no, false, 0]) ->
			Value = false
		;	format('Invalid input, defaulting to false~n', []),
			Value = false
		),
		retractall(widget_value(WidgetId, _)),
		assertz(widget_value(WidgetId, Value)),
		format('Value set: ~w~n', [Value]),
		format('===================~n~n', []).

	% Set widget value directly
	set_widget_value(WidgetId, Value) :-
		retractall(widget_value(WidgetId, _)),
		assertz(widget_value(WidgetId, Value)).

	% Get widget value
	get_widget_value(WidgetId, Value) :-
		widget_value(WidgetId, Value).

	% Check if widget exists
	widget_exists(WidgetId) :-
		widget_value(WidgetId, _).

	% List all widgets
	list_widgets :-
		format('~n=== WIDGET VALUES ===~n', []),
		(	widget_value(WidgetId, Value) ->
			format('~w: ~w~n', [WidgetId, Value]),
			fail
		;	true
		),
		format('====================~n~n', []).

	% Clear all widgets
	clear_widgets :-
		retractall(widget_value(_, _)),
		format('All widget values cleared.~n', []).

:- end_object.
