%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%
%  This file is part of Logtalk <https://logtalk.org/>
%  SPDX-FileCopyrightText: 1998-2025 <PERSON> <<EMAIL>>
%  SPDX-License-Identifier: Apache-2.0
%
%  Licensed under the Apache License, Version 2.0 (the "License");
%  you may not use this file except in compliance with the License.
%  You may obtain a copy of the License at
%
%      http://www.apache.org/licenses/LICENSE-2.0
%
%  Unless required by applicable law or agreed to in writing, software
%  distributed under the License is distributed on an "AS IS" BASIS,
%  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
%  See the License for the specific language governing permissions and
%  limitations under the License.
%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%


% This object provides predicates for creating and managing HTML/JavaScript widgets
% in Logtalk Jupyter notebooks.


:- object(jupyter_widget_handling).

	:- info([
		version is 0:1:0,
		author is 'Paulo Moura',
		date is 2025-01-27,
		comment is 'This object provides predicates for creating and managing HTML/JavaScript widgets in Logtalk notebooks.'
	]).

	:- public([
		create_text_input/3,        % create_text_input(+WidgetId, +Label, +DefaultValue)
		create_number_input/4,      % create_number_input(+WidgetId, +Label, +DefaultValue, +Options)
		create_slider/5,            % create_slider(+WidgetId, +Label, +Min, +Max, +DefaultValue)
		create_dropdown/3,          % create_dropdown(+WidgetId, +Label, +Options)
		create_checkbox/3,          % create_checkbox(+WidgetId, +Label, +DefaultValue)
		create_button/2,            % create_button(+WidgetId, +Label)
		get_widget_value/2,         % get_widget_value(+WidgetId, -Value)
		set_widget_value/2,         % set_widget_value(+WidgetId, +Value)
		remove_widget/1,            % remove_widget(+WidgetId)
		clear_all_widgets/0,        % clear_all_widgets
		widget_exists/1,            % widget_exists(+WidgetId)
		debug_widgets/0,            % debug_widgets
		list_all_widgets/1          % list_all_widgets(-Widgets)
	]).

	:- uses(jupyter_logging, [log/1, log/2]).
	:- uses(jupyter_term_handling, [assert_success_response/4]).

	% Dynamic predicate to store widget state
	:- dynamic(widget_state/3).  % widget_state(WidgetId, Type, Value)

	% Widget counter for generating unique IDs
	:- dynamic(widget_counter/1).
	widget_counter(0).

	% Generate unique widget ID
	generate_widget_id(WidgetId) :-
		retract(widget_counter(N)),
		N1 is N + 1,
		assertz(widget_counter(N1)),
		atomic_list_concat(['widget_', N1], WidgetId).

	% Create text input widget
	create_text_input(WidgetId, Label, DefaultValue) :-
		(	var(WidgetId) ->
			generate_widget_id(WidgetId)
		;	true
		),
		assertz(widget_state(WidgetId, text_input, DefaultValue)),
		create_text_input_html(WidgetId, Label, DefaultValue, HTML),
		assert_success_response(widget, [], '', [widget_html-HTML]).

	% Create number input widget
	create_number_input(WidgetId, Label, DefaultValue, Options) :-
		(	var(WidgetId) ->
			generate_widget_id(WidgetId)
		;	true
		),
		assertz(widget_state(WidgetId, number_input, DefaultValue)),
		create_number_input_html(WidgetId, Label, DefaultValue, Options, HTML),
		assert_success_response(widget, [], '', [widget_html-HTML]).

	% Create slider widget
	create_slider(WidgetId, Label, Min, Max, DefaultValue) :-
		(	var(WidgetId) ->
			generate_widget_id(WidgetId)
		;	true
		),
		assertz(widget_state(WidgetId, slider, DefaultValue)),
		create_slider_html(WidgetId, Label, Min, Max, DefaultValue, HTML),
		assert_success_response(widget, [], '', [widget_html-HTML]).

	% Create dropdown widget
	create_dropdown(WidgetId, Label, Options) :-
		(	var(WidgetId) ->
			generate_widget_id(WidgetId)
		;	true
		),
		Options = [FirstOption|_],
		assertz(widget_state(WidgetId, dropdown, FirstOption)),
		create_dropdown_html(WidgetId, Label, Options, HTML),
		assert_success_response(widget, [], '', [widget_html-HTML]).

	% Create checkbox widget
	create_checkbox(WidgetId, Label, DefaultValue) :-
		(	var(WidgetId) ->
			generate_widget_id(WidgetId)
		;	true
		),
		assertz(widget_state(WidgetId, checkbox, DefaultValue)),
		create_checkbox_html(WidgetId, Label, DefaultValue, HTML),
		assert_success_response(widget, [], '', [widget_html-HTML]).

	% Create button widget
	create_button(WidgetId, Label) :-
		(	var(WidgetId) ->
			generate_widget_id(WidgetId)
		;	true
		),
		assertz(widget_state(WidgetId, button, clicked)),
		create_button_html(WidgetId, Label, HTML),
		assert_success_response(widget, [], '', [widget_html-HTML]).

	% Get widget value
	get_widget_value(WidgetId, Value) :-
		widget_state(WidgetId, _, Value).

	% Set widget value
	set_widget_value(WidgetId, Value) :-
		retract(widget_state(WidgetId, Type, _)),
		assertz(widget_state(WidgetId, Type, Value)).

	% Remove widget
	remove_widget(WidgetId) :-
		retractall(widget_state(WidgetId, _, _)).

	% Clear all widgets
	clear_all_widgets :-
		retractall(widget_state(_, _, _)).

	% Check if widget exists
	widget_exists(WidgetId) :-
		widget_state(WidgetId, _, _).

	% Debug widgets - print all widget states
	debug_widgets :-
		write('=== Widget Debug Information ==='), nl,
		(	widget_state(WidgetId, Type, Value) ->
			format('Widget ~w: Type=~w, Value=~w~n', [WidgetId, Type, Value]),
			fail
		;	true
		),
		write('=== End Widget Debug ==='), nl.

	% List all widgets
	list_all_widgets(Widgets) :-
		findall(widget(WidgetId, Type, Value), widget_state(WidgetId, Type, Value), Widgets).

	% Check localStorage for widget values (fallback method)
	:- public(check_localstorage_widgets/0).
	check_localstorage_widgets :-
		format('~n=== CHECKING LOCALSTORAGE ===~n', []),
		jupyter_term_handling::assert_success_response(check_storage, [], '', [
			widget_html-'<script>console.log("Checking localStorage for widget values..."); const widgets = []; for (let i = 0; i < localStorage.length; i++) { const key = localStorage.key(i); if (key && key.startsWith("logtalk_widget_")) { try { const data = JSON.parse(localStorage.getItem(key)); widgets.push(data); console.log("Found widget in localStorage:", data); } catch (e) { console.log("Failed to parse widget data for key:", key); } } } if (widgets.length > 0) { console.log("Total widgets found in localStorage:", widgets.length); } else { console.log("No widgets found in localStorage"); }</script>'
		]),
		format('LocalStorage check completed.~n', []).

	% HTML generation predicates

	% Generate text input HTML
	create_text_input_html(WidgetId, Label, DefaultValue, HTML) :-
		atomic_list_concat([
			'<div class="logtalk-widget" id="container_', WidgetId, '">',
			'<label for="', WidgetId, '">', Label, '</label><br>',
			'<input type="text" id="', WidgetId, '" value="', DefaultValue, '" ',
			'onchange="updateLogtalkWidget_', WidgetId, '(this.value)" ',
			'style="margin: 5px; padding: 5px; border: 1px solid #ccc; border-radius: 3px;">',
			'</div>',
			'<script>',
			'function updateLogtalkWidget_', WidgetId, '(value) {',
			'  console.log("Widget update called:", "', WidgetId, '", value);',
			'  try {',
			'    const escapedValue = typeof value === "string" ? "\\'" + value.replace(/\\\\/g, "\\\\\\\\").replace(/\\'/g, "\\\\\\'") + "\\'" : String(value);',
			'    const code = "jupyter_widget_handling::set_widget_value(\\\'', WidgetId, '\\\', " + escapedValue + ").";',
			'    console.log("Executing:", code);',
			'    ',
			'    let executed = false;',
			'    if (typeof window._JUPYTERLAB !== "undefined") {',
			'      console.log("Trying _JUPYTERLAB approach");',
			'    }',
			'    ',
			'    if (!executed) {',
			'      console.log("Storing in localStorage as fallback");',
			'      const widgetData = { widgetId: "', WidgetId, '", value: value, code: code, timestamp: Date.now() };',
			'      localStorage.setItem("logtalk_widget_', WidgetId, '", JSON.stringify(widgetData));',
			'      console.log("Stored widget data:", widgetData);',
			'    }',
			'  } catch (e) {',
			'    console.error("Widget update failed:", e);',
			'  }',
			'}',
			'console.log("Widget function created for:', WidgetId, '");',
			'</script>'
		], HTML).

	% Generate number input HTML
	create_number_input_html(WidgetId, Label, DefaultValue, Options, HTML) :-
		extract_number_options(Options, MinAttr, MaxAttr, StepAttr),
		atomic_list_concat([
			'<div class="logtalk-widget" id="container_', WidgetId, '">',
			'<label for="', WidgetId, '">', Label, '</label><br>',
			'<input type="number" id="', WidgetId, '" value="', DefaultValue, '" ',
			MinAttr, ' ', MaxAttr, ' ', StepAttr, ' ',
			'onchange="updateLogtalkWidget(\'', WidgetId, '\', parseFloat(this.value))" ',
			'style="margin: 5px; padding: 5px; border: 1px solid #ccc; border-radius: 3px;">',
			'</div>',
			'<script>',
			'setTimeout(function() {',
			'  if (typeof autoRegisterWidget === "function") {',
			'    autoRegisterWidget("', WidgetId, '", "number_input", ', DefaultValue, ');',
			'  }',
			'}, 100);',
			'</script>'
		], HTML).

	% Generate slider HTML
	create_slider_html(WidgetId, Label, Min, Max, DefaultValue, HTML) :-
		atomic_list_concat([
			'<div class="logtalk-widget" id="container_', WidgetId, '">',
			'<label for="', WidgetId, '">', Label, ': <span id="', WidgetId, '_value">', DefaultValue, '</span></label><br>',
			'<input type="range" id="', WidgetId, '" min="', Min, '" max="', Max, '" value="', DefaultValue, '" ',
			'oninput="document.getElementById(\'', WidgetId, '_value\').textContent = this.value; updateLogtalkWidget(\'', WidgetId, '\', parseFloat(this.value))" ',
			'style="margin: 5px; width: 200px;">',
			'</div>',
			'<script>',
			'setTimeout(function() {',
			'  if (typeof autoRegisterWidget === "function") {',
			'    autoRegisterWidget("', WidgetId, '", "slider", ', DefaultValue, ');',
			'  }',
			'}, 100);',
			'</script>'
		], HTML).

	% Generate dropdown HTML
	create_dropdown_html(WidgetId, Label, Options, HTML) :-
		create_option_elements(Options, OptionElements),
		atomic_list_concat([
			'<div class="logtalk-widget" id="container_', WidgetId, '">',
			'<label for="', WidgetId, '">', Label, '</label><br>',
			'<select id="', WidgetId, '" ',
			'onchange="updateLogtalkWidget(\'', WidgetId, '\', this.value)" ',
			'style="margin: 5px; padding: 5px; border: 1px solid #ccc; border-radius: 3px;">',
			OptionElements,
			'</select>',
			'</div>'
		], HTML).

	% Generate checkbox HTML
	create_checkbox_html(WidgetId, Label, DefaultValue, HTML) :-
		(	DefaultValue == true ->
			CheckedAttr = 'checked'
		;	CheckedAttr = ''
		),
		atomic_list_concat([
			'<div class="logtalk-widget" id="container_', WidgetId, '">',
			'<input type="checkbox" id="', WidgetId, '" ', CheckedAttr, ' ',
			'onchange="updateLogtalkWidget(\'', WidgetId, '\', this.checked)" ',
			'style="margin: 5px;">',
			'<label for="', WidgetId, '">', Label, '</label>',
			'</div>'
		], HTML).

	% Generate button HTML
	create_button_html(WidgetId, Label, HTML) :-
		atomic_list_concat([
			'<div class="logtalk-widget" id="container_', WidgetId, '">',
			'<button id="', WidgetId, '" ',
			'onclick="updateLogtalkWidget(\'', WidgetId, '\', \'clicked\')" ',
			'style="margin: 5px; padding: 8px 16px; background-color: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer;">',
			Label,
			'</button>',
			'</div>'
		], HTML).

	% Helper predicates

	% Extract number input options
	extract_number_options([], '', '', '').
	extract_number_options([min(Min)|Rest], MinAttr, MaxAttr, StepAttr) :-
		atomic_list_concat(['min="', Min, '"'], MinAttr),
		extract_number_options(Rest, _, MaxAttr, StepAttr).
	extract_number_options([max(Max)|Rest], MinAttr, MaxAttr, StepAttr) :-
		atomic_list_concat(['max="', Max, '"'], MaxAttr),
		extract_number_options(Rest, MinAttr, _, StepAttr).
	extract_number_options([step(Step)|Rest], MinAttr, MaxAttr, StepAttr) :-
		atomic_list_concat(['step="', Step, '"'], StepAttr),
		extract_number_options(Rest, MinAttr, MaxAttr, _).
	extract_number_options([_|Rest], MinAttr, MaxAttr, StepAttr) :-
		extract_number_options(Rest, MinAttr, MaxAttr, StepAttr).

	% Create option elements for dropdown
	create_option_elements([], '').
	create_option_elements([Option|Rest], OptionElements) :-
		atomic_list_concat(['<option value="', Option, '">', Option, '</option>'], OptionElement),
		create_option_elements(Rest, RestElements),
		atomic_list_concat([OptionElement, RestElements], OptionElements).

:- end_object.
