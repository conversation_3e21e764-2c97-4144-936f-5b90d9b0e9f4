{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Working Widget Test\n", "\n", "Step-by-step test to verify widget capture and sync works correctly."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Create Widget"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::create_text_input(working_test, 'Working Test:', 'original_value')."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Check Initial Server Value"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::get_widget_value(working_test, Value),\n", "format('Server value: ~w~n', [Value])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Interact with Widget\n", "\n", "1. **Type 'hello_world'** in the text input above\n", "2. **Click the \"Update\" button**\n", "3. **You should see an alert** saying \"Value captured: hello_world\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Check What Was Captured\n", "\n", "Run this in browser console (F12):\n", "\n", "```javascript\n", "console.log('Captured value:', localStorage.getItem('widget_working_test'));\n", "```\n", "\n", "You should see: `Captured value: hello_world`"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Sync the Captured Value\n", "\n", "**Replace 'hello_world' with whatever you actually typed:**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter_widget_handling::sync_widget_value(working_test, 'hello_world')."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Verify the Sync Worked"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::get_widget_value(working_test, Value),\n", "format('Updated server value: ~w~n', [Value])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 7: Debug Widget State"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter_widget_handling::debug_widgets."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Expected Results\n", "\n", "- **Step 2**: Shows `original_value`\n", "- **Step 3**: <PERSON><PERSON> shows your typed value\n", "- **Step 4**: <PERSON><PERSON><PERSON> shows the captured value\n", "- **Step 5**: Shows `✅ Synced widget working_test with value: hello_world`\n", "- **Step 6**: Shows your typed value (not `original_value`)\n", "- **Step 7**: Shows the widget state with updated value\n", "\n", "## Troubleshooting\n", "\n", "### If Step 6 still shows the original value:\n", "1. Make sure you used the exact value from Step 4\n", "2. Check that Step 5 showed the success message\n", "3. Try running Step 7 to see the internal state\n", "\n", "### If the alert doesn't appear:\n", "1. Check browser console for JavaScript errors\n", "2. Make sure you clicked the \"Update\" button (not just typed)\n", "\n", "## Automation Ideas\n", "\n", "If this manual process works, we can automate it by:\n", "1. **Creating a helper function** that reads from localStorage\n", "2. **Adding a \"Sync All\" button** to sync multiple widgets\n", "3. **Implementing polling** to auto-sync periodically\n", "\n", "The key is that we now have a working foundation!"]}], "metadata": {"kernelspec": {"display_name": "Logtalk", "language": "logtalk", "name": "logtalk"}, "language_info": {"codemirror_mode": "prolog", "file_extension": ".lgt", "name": "logtalk", "pygments_lexer": "prolog", "version": "3.0"}}, "nbformat": 4, "nbformat_minor": 4}