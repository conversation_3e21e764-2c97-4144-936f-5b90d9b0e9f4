jupyter::create_text_input(test_widget, 'Test Input:', 'initial_value').

jupyter::debug_widgets.

jupyter::get_widget_value(test_widget, Value),
write('Widget value: '), write(Value), nl.

jupyter_widget_handling::set_widget_value(test_widget, 'manually_set_value').

jupyter::get_widget_value(test_widget, Value),
write('Widget value after manual set: '), write(Value), nl.

jupyter::list_all_widgets(Widgets),
write('All widgets: '), write(Widgets), nl.

%%html
<script>
// Manual kernel connection and debugging
console.log('=== Logtalk Widget Debug ===');
console.log('LogtalkWidgets available:', typeof LogtalkWidgets !== 'undefined');
console.log('Jupyter available:', typeof Jupyter !== 'undefined');
console.log('Jupyter.notebook available:', typeof Jupyter !== 'undefined' && !!Jupyter.notebook);
console.log('Kernel available:', typeof Jupyter !== 'undefined' && Jupyter.notebook && !!Jupyter.notebook.kernel);

// Try to connect manually
if (typeof connectLogtalkWidgetsToKernel !== 'undefined') {
    const connected = connectLogtalkWidgetsToKernel();
    console.log('Manual connection result:', connected);
} else {
    console.log('Manual connection function not available');
}

// Debug widget state
if (typeof LogtalkWidgets !== 'undefined') {
    LogtalkWidgets.debugWidgets();
}

// Test widget update
if (typeof updateLogtalkWidget !== 'undefined') {
    console.log('Testing widget update...');
    updateLogtalkWidget('test_widget', 'javascript_test_value');
} else {
    console.log('updateLogtalkWidget function not available');
}
</script>