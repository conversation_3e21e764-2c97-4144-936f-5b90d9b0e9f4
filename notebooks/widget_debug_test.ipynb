{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Widget Debug Test\n", "\n", "This notebook helps debug widget communication issues."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Create a simple text widget"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/html": ["\n", "        <div class=\"logtalk-widget-container\">\n", "            <div class=\"logtalk-widget\" id=\"container_test_widget\"><label for=\"test_widget\">Test Input:</label><br><input type=\"text\" id=\"test_widget\" value=\"initial_value\" onchange=\"updateLogtalkWidget('test_widget', this.value)\" style=\"margin: 5px; padding: 5px; border: 1px solid #ccc; border-radius: 3px;\"></div><script>setTimeout(function() {  if (typeof autoRegisterWidget === \"function\") {    autoRegisterWidget(\"test_widget\", \"text_input\", \"initial_value\");  }}, 100);</script>\n", "        </div>\n", "        <script>\n", "            // Ensure widget library is loaded only once\n", "            if (typeof LogtalkWidgets === 'undefined') {\n", "                /**\n", " * Logtalk Jupyter <PERSON> Widget Support\n", " * JavaScript communication layer for HTML/JavaScript widgets\n", " */\n", "\n", "// Global widget state management\n", "window.LogtalkWidgets = {\n", "    widgets: new Map(),\n", "    kernel: null,\n", "    \n", "    // Initialize widget system\n", "    init: function() {\n", "        // Get reference to <PERSON><PERSON><PERSON> kernel\n", "        if (typeof Jupyter !== 'undefined' && Jupyter.notebook) {\n", "            this.kernel = Jupyter.notebook.kernel;\n", "        } else if (typeof window.parent !== 'undefined' && window.parent.Jupyter) {\n", "            this.kernel = window.parent.Jupyter.notebook.kernel;\n", "        }\n", "        \n", "        console.log('Logtalk Widgets initialized');\n", "    },\n", "    \n", "    // Register a widget\n", "    registerWidget: function(widgetId, type, initialValue) {\n", "        this.widgets.set(widgetId, {\n", "            type: type,\n", "            value: initialValue,\n", "            element: document.getElementById(widgetId)\n", "        });\n", "    },\n", "    \n", "    // Update widget value and notify kernel\n", "    updateWidget: function(widgetId, value) {\n", "        if (this.widgets.has(widgetId)) {\n", "            const widget = this.widgets.get(widgetId);\n", "            widget.value = value;\n", "            \n", "            // Send update to kernel\n", "            this.sendWidgetUpdate(widgetId, value);\n", "        }\n", "    },\n", "    \n", "    // Send widget update to Logtalk kernel\n", "    sendWidgetUpdate: function(widgetId, value) {\n", "        if (!this.kernel) {\n", "            console.warn('Kernel not available for widget update');\n", "            return;\n", "        }\n", "\n", "        // Properly escape the value for <PERSON><PERSON><PERSON>\n", "        let escapedValue;\n", "        if (typeof value === 'string') {\n", "            // Escape single quotes and backslashes for Logtalk string literals\n", "            escapedValue = value.replace(/\\\\/g, '\\\\\\\\').replace(/'/g, \"\\\\'\");\n", "            escapedValue = `'${escapedValue}'`;\n", "        } else if (typeof value === 'boolean') {\n", "            escapedValue = value ? 'true' : 'false';\n", "        } else {\n", "            escapedValue = String(value);\n", "        }\n", "\n", "        const code = `jupyter_widget_handling::set_widget_value('${widgetId}', ${escapedValue}).`;\n", "\n", "        console.log('Sending widget update:', code);\n", "\n", "        this.kernel.execute(code, {\n", "            silent: true,\n", "            store_history: false,\n", "            user_expressions: {},\n", "            allow_stdin: false,\n", "            stop_on_error: false\n", "        });\n", "    },\n", "    \n", "    // Get widget value\n", "    getWidgetValue: function(widgetId) {\n", "        if (this.widgets.has(widgetId)) {\n", "            return this.widgets.get(widgetId).value;\n", "        }\n", "        return null;\n", "    },\n", "    \n", "    // Set widget value from kernel\n", "    setWidgetValue: function(widgetId, value) {\n", "        if (this.widgets.has(widgetId)) {\n", "            const widget = this.widgets.get(widgetId);\n", "            widget.value = value;\n", "            \n", "            // Update DOM element\n", "            const element = widget.element;\n", "            if (element) {\n", "                switch (widget.type) {\n", "                    case 'text_input':\n", "                    case 'number_input':\n", "                        element.value = value;\n", "                        break;\n", "                    case 'slider':\n", "                        element.value = value;\n", "                        // Update display value\n", "                        const valueDisplay = document.getElementById(widgetId + '_value');\n", "                        if (valueDisplay) {\n", "                            valueDisplay.textContent = value;\n", "                        }\n", "                        break;\n", "                    case 'dropdown':\n", "                        element.value = value;\n", "                        break;\n", "                    case 'checkbox':\n", "                        element.checked = (value === true || value === 'true');\n", "                        break;\n", "                }\n", "            }\n", "        }\n", "    },\n", "    \n", "    // Remove widget\n", "    removeWidget: function(widgetId) {\n", "        if (this.widgets.has(widgetId)) {\n", "            this.widgets.delete(widgetId);\n", "            \n", "            // Remove DOM element\n", "            const container = document.getElementById('container_' + widgetId);\n", "            if (container) {\n", "                container.remove();\n", "            }\n", "        }\n", "    },\n", "    \n", "    // Clear all widgets\n", "    clearAllWidgets: function() {\n", "        this.widgets.forEach((widget, widgetId) => {\n", "            this.removeWidget(widgetId);\n", "        });\n", "        this.widgets.clear();\n", "    },\n", "\n", "    // Debug function to check widget state\n", "    debugWidgets: function() {\n", "        console.log('Registered widgets:', this.widgets);\n", "        console.log('Kernel available:', !!this.kernel);\n", "        this.widgets.forEach((widget, widgetId) => {\n", "            console.log(`Widget ${widgetId}:`, {\n", "                type: widget.type,\n", "                value: widget.value,\n", "                element: widget.element,\n", "                elementValue: widget.element ? widget.element.value : 'N/A'\n", "            });\n", "        });\n", "    }\n", "};\n", "\n", "// Global function for widget updates (called from HTML)\n", "function updateLogtalkWidget(widgetId, value) {\n", "    LogtalkWidgets.updateWidget(widgetId, value);\n", "}\n", "\n", "// Auto-register widgets when they are created\n", "function autoRegisterWidget(widgetId, type, initialValue) {\n", "    LogtalkWidgets.registerWidget(widgetId, type, initialValue);\n", "}\n", "\n", "// Initialize when DOM is ready\n", "if (document.readyState === 'loading') {\n", "    document.addEventListener('DOMContentLoaded', function() {\n", "        LogtalkWidgets.init();\n", "    });\n", "} else {\n", "    LogtalkWidgets.init();\n", "}\n", "\n", "// Initialize when <PERSON><PERSON><PERSON> is ready (for notebook environment)\n", "if (typeof Jupyter !== 'undefined') {\n", "    Jupyter.notebook.events.on('kernel_ready.Kernel', function() {\n", "        LogtalkWidgets.init();\n", "    });\n", "}\n", "\n", "// CSS styles for widgets\n", "const widgetStyles = `\n", "<style>\n", ".logtalk-widget {\n", "    margin: 10px 0;\n", "    padding: 10px;\n", "    border: 1px solid #e0e0e0;\n", "    border-radius: 5px;\n", "    background-color: #fafafa;\n", "    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n", "}\n", "\n", ".logtalk-widget label {\n", "    font-weight: 500;\n", "    color: #333;\n", "    margin-bottom: 5px;\n", "    display: inline-block;\n", "}\n", "\n", ".logtalk-widget input[type=\"text\"],\n", ".logtalk-widget input[type=\"number\"],\n", ".logtalk-widget select {\n", "    width: 200px;\n", "    padding: 6px 10px;\n", "    border: 1px solid #ccc;\n", "    border-radius: 4px;\n", "    font-size: 14px;\n", "    transition: border-color 0.3s ease;\n", "}\n", "\n", ".logtalk-widget input[type=\"text\"]:focus,\n", ".logtalk-widget input[type=\"number\"]:focus,\n", ".logtalk-widget select:focus {\n", "    outline: none;\n", "    border-color: #007cba;\n", "    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2);\n", "}\n", "\n", ".logtalk-widget input[type=\"range\"] {\n", "    width: 200px;\n", "    margin: 5px 0;\n", "}\n", "\n", ".logtalk-widget input[type=\"checkbox\"] {\n", "    margin-right: 8px;\n", "    transform: scale(1.2);\n", "}\n", "\n", ".logtalk-widget button {\n", "    background-color: #007cba;\n", "    color: white;\n", "    border: none;\n", "    padding: 8px 16px;\n", "    border-radius: 4px;\n", "    cursor: pointer;\n", "    font-size: 14px;\n", "    transition: background-color 0.3s ease;\n", "}\n", "\n", ".logtalk-widget button:hover {\n", "    background-color: #005a87;\n", "}\n", "\n", ".logtalk-widget button:active {\n", "    background-color: #004a73;\n", "}\n", "</style>\n", "`;\n", "\n", "// Inject CSS styles\n", "if (document.head) {\n", "    document.head.insertAdjacentHTML('beforeend', widgetStyles);\n", "}\n", "\n", "                console.log('Logtalk widget library loaded');\n", "            } else {\n", "                console.log('Logtalk widget library already available');\n", "            }\n", "\n", "            // Ensure kernel reference is updated\n", "            if (typeof LogtalkWidgets !== 'undefined') {\n", "                LogtalkWidgets.init();\n", "            }\n", "        </script>\n", "        "], "text/plain": ["Logtalk Widget"]}, "metadata": {"text/html": {"isolated": false}}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["jupyter::create_text_input(test_widget, 'Test Input:', 'initial_value')."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Check widget state on server"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["=== Widget Debug Information ===\n", "Widget test_widget: Type=text_input, Value=initial_value"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1;31mfalse"]}, "metadata": {}, "output_type": "display_data"}], "source": ["jupyter::debug_widgets."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Try to get widget value"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["Widget value: initial_value"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mValue = initial_value"]}, "metadata": {}, "output_type": "display_data"}], "source": ["jupyter::get_widget_value(test_widget, Value),\n", "write('Widget value: '), write(Value), nl."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Manually set widget value"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["jupyter_widget_handling::set_widget_value(test_widget, 'manually_set_value')."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Check value again"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["Widget value after manual set: manually_set_value"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mValue = manually_set_value"]}, "metadata": {}, "output_type": "display_data"}], "source": ["jupyter::get_widget_value(test_widget, Value),\n", "write('Widget value after manual set: '), write(Value), nl."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: List all widgets"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["All widgets: [widget(test_widget,text_input,manually_set_value)]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mWidgets = [widget(test_widget,text_input,manually_set_value)]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["jupyter::list_all_widgets(Widgets),\n", "write('All widgets: '), write(Widgets), nl."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 7: Manual Kernel Connection (if needed)\n", "\n", "If you see \"Kernel not available\" errors, run this JavaScript in the browser console:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%html\n", "<script>\n", "// Manual kernel connection and debugging\n", "console.log('=== Logtalk Widget Debug ===');\n", "console.log('LogtalkWidgets available:', typeof LogtalkWidgets !== 'undefined');\n", "console.log('Jupyter available:', typeof Jupyter !== 'undefined');\n", "console.log('Jupyter.notebook available:', typeof Jupyter !== 'undefined' && !!Jupyter.notebook);\n", "console.log('Kernel available:', typeof Jupyter !== 'undefined' && Jupyter.notebook && !!Jupyter.notebook.kernel);\n", "\n", "// Try to connect manually\n", "if (typeof connectLogtalkWidgetsToKernel !== 'undefined') {\n", "    const connected = connectLogtalkWidgetsToKernel();\n", "    console.log('Manual connection result:', connected);\n", "} else {\n", "    console.log('Manual connection function not available');\n", "}\n", "\n", "// Debug widget state\n", "if (typeof LogtalkWidgets !== 'undefined') {\n", "    LogtalkWidgets.debugWidgets();\n", "}\n", "\n", "// Test widget update\n", "if (typeof updateLogtalkWidget !== 'undefined') {\n", "    console.log('Testing widget update...');\n", "    updateLogtalkWidget('test_widget', 'javascript_test_value');\n", "} else {\n", "    console.log('updateLogtalkWidget function not available');\n", "}\n", "</script>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## JavaScript Debug Commands\n", "\n", "You can also run these commands directly in the browser console:\n", "\n", "```javascript\n", "// Check system status\n", "console.log('LogtalkWidgets:', typeof LogtalkWidgets);\n", "console.log('<PERSON>py<PERSON>:', typeof Jupy<PERSON>);\n", "console.log('Kernel:', Jupyter && Jupyter.notebook && Jupyter.notebook.kernel);\n", "\n", "// Manual connection\n", "connectLogtalkWidgetsToKernel();\n", "\n", "// Debug widget state\n", "LogtalkWidgets.debugWidgets();\n", "\n", "// Test widget update\n", "updateLogtalkWidget('test_widget', 'manual_test_value');\n", "```"]}], "metadata": {"kernelspec": {"display_name": "Logtalk", "language": "logtalk", "name": "logtalk_kernel"}, "language_info": {"codemirror_mode": "logtalk", "file_extension": ".lgt", "mimetype": "text/x-logtalk", "name": "Logtalk"}}, "nbformat": 4, "nbformat_minor": 4}