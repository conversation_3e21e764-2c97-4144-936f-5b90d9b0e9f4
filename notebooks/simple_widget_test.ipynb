jupyter::prompt_text_input(test_name, 'Enter your name:', 'Test User').

jupyter_vscode_widgets::get_widget_value(test_name, Name),
format('Hello, ~w!~n', [Name]).

jupyter::prompt_number_input(test_age, 'Enter your age:', 30).

jupyter_vscode_widgets::get_widget_value(test_age, Age),
format('You are ~w years old.~n', [Age]).

jupyter::prompt_choice(test_color, 'Pick a color:', [red, blue, green]).

jupyter_vscode_widgets::get_widget_value(test_color, Color),
format('You chose: ~w~n', [Color]).

jupyter::prompt_yes_no(test_answer, 'Do you like this widget system?').

jupyter_vscode_widgets::get_widget_value(test_answer, Answer),
format('Your answer: ~w~n', [Answer]).

jupyter::list_widgets.