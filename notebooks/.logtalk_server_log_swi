json([jsonrpc-2.0,id-0,result-true])
json([jsonrpc-2.0,id-1,result-json([1-json([status-success,type-debug,bindings-json([]),output-,widget_html- <script>console.log("=== JupyterLab Environment Debug ==="); console.log("window.jupyterapp:", typeof window.jupyterapp); console.log("Jupyter:", typeof Jupyter); console.log("IPython:", typeof IPython); if (typeof window.jupyterapp !== "undefined") { console.log("JupyterLab app:", window.jupyterapp); console.log("Current widget:", window.jupyterapp.shell ? window.jupyterapp.shell.currentWidget : "no shell"); if (window.jupyterapp.shell && window.jupyterapp.shell.currentWidget) { const widget = window.jupyterapp.shell.currentWidget; console.log("Widget context:", widget.context); if (widget.context && widget.context.sessionContext) { console.log("Session context:", widget.context.sessionContext); console.log("Session:", widget.context.sessionContext.session); if (widget.context.sessionContext.session) { console.log("Kernel:", widget.context.sessionContext.session.kernel); } } } } console.log("=== End Debug ===");</script>]),2-json([status-success,type-query,bindings-json([]),output-])])])
json([jsonrpc-2.0,id-2,result-json([1-json([status-success,type-widget,bindings-json([]),output-,widget_html- <div class="logtalk-widget" id="container_lab_test_widget"><label for="lab_test_widget">Test in JupyterLab:</label><br><input type="text" id="lab_test_widget" value="Hello JupyterLab" onchange="updateLogtalkWidget('lab_test_widget', this.value)" style="margin: 5px; padding: 5px; border: 1px solid #ccc; border-radius: 3px;"></div><script>setTimeout(function() {  if (typeof autoRegisterWidget === "function") {    autoRegisterWidget("lab_test_widget", "text_input", "Hello JupyterLab");  }}, 100);</script>]),2-json([status-success,type-query,bindings-json([]),output-])])])
json([jsonrpc-2.0,id-3,result-json([1-json([status-success,type-kernel_test,bindings-json([]),output-,widget_html- <script>console.log("Testing manual kernel execution..."); function testJupyterLabKernel() { if (typeof window.jupyterapp !== "undefined" && window.jupyterapp.shell) { try { const currentWidget = window.jupyterapp.shell.currentWidget; if (currentWidget && currentWidget.context && currentWidget.context.sessionContext) { const session = currentWidget.context.sessionContext.session; if (session && session.kernel) { console.log("Found JupyterLab kernel:", session.kernel); const testCode = "write('Manual kernel test successful!'), nl."; console.log("Executing test code:", testCode); const future = session.kernel.requestExecute({ code: testCode }); console.log("Execution future:", future); return true; } } } catch (e) { console.error("JupyterLab kernel test failed:", e); } } console.log("JupyterLab kernel not available"); return false; } const result = testJupyterLabKernel(); console.log("Manual kernel test result:", result);</script>]),2-json([status-success,type-query,bindings-json([]),output-])])])
json([jsonrpc-2.0,id-4,result-json([1-json([status-success,type-query,bindings-json([Value-'Hello JupyterLab']),output-Widget value: Hello JupyterLab])])])
json([jsonrpc-2.0,id-5,result-json([1-json([status-error,error-json([code- -4711,message-Failure,data-json([logtalk_message-,output- === Widget Debug Information ===
Widget lab_test_widget: Type=text_input, Value=Hello JupyterLab])])])])])
