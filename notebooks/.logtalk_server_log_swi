json([jsonrpc-2.0,id-0,result-true])
json([jsonrpc-2.0,id-1,result-json([1-json([status-success,type-widget,bindings-json([]),output-,widget_html- <div class="logtalk-widget" id="container_name_input"><label for="name_input">Enter your name:</label><br><input type="text" id="name_input" value="John Doe" onchange="updateLogtalkWidget('name_input', this.value)" style="margin: 5px; padding: 5px; border: 1px solid #ccc; border-radius: 3px;"></div><script>setTimeout(function() {  if (typeof autoRegisterWidget === "function") {    autoRegisterWidget("name_input", "text_input", "John Doe");  }}, 100);</script>]),2-json([status-success,type-query,bindings-json([]),output-])])])
