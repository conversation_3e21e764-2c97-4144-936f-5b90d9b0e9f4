json([jsonrpc-2.0,id-0,result-true])
json([jsonrpc-2.0,id-1,result-json([1-json([status-success,type-widget,bindings-json([]),output-,widget_html- <div class="logtalk-widget" id="container_working_test"><label for="working_test">Working Test:</label><br><input type="text" id="working_test" value="original_value" style="margin: 5px; padding: 5px; border: 1px solid #ccc; border-radius: 3px;"><button onclick="captureValue_working_test()" style="margin-left: 5px; padding: 5px;">Update</button></div><script>function captureValue_working_test() {  var input = document.getElementById("working_test");  var value = input.value;  console.log("Captured value for working_test:", value);  localStorage.setItem("widget_working_test", value);  alert("Value captured: " + value);}</script>]),2-json([status-success,type-query,bindings-json([]),output-])])])
json([jsonrpc-2.0,id-2,result-json([1-json([status-success,type-query,bindings-json([Value-original_value]),output-Server value: original_value])])])
json([jsonrpc-2.0,id-3,result-json([1-json([status-success,type-query,bindings-json([]),output- ✅ Synced widget working_test with value: hello_world])])])
json([jsonrpc-2.0,id-4,result-json([1-json([status-success,type-query,bindings-json([Value-hello_world]),output-Updated server value: hello_world])])])
json([jsonrpc-2.0,id-5,result-json([1-json([status-error,error-json([code- -4711,message-Failure,data-json([logtalk_message-,output- === Widget Debug Information ===
Widget working_test: Type=text_input, Value=hello_world])])])])])
