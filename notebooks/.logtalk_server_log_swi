json([jsonrpc-2.0,id-0,result-true])
json([jsonrpc-2.0,id-1,result-json([1-json([status-success,type-widget,bindings-json([]),output-,widget_html- <div class="logtalk-widget" id="container_final_test"><label for="final_test">Final Test Widget:</label><br><input type="text" id="final_test" value="initial_value" onchange="updateLogtalkWidget('final_test', this.value)" style="margin: 5px; padding: 5px; border: 1px solid #ccc; border-radius: 3px;"></div><script>setTimeout(function() {  if (typeof autoRegisterWidget === "function") {    autoRegisterWidget("final_test", "text_input", "initial_value");  }}, 100);</script>]),2-json([status-success,type-query,bindings-json([]),output-])])])
json([jsonrpc-2.0,id-2,result-json([1-json([status-success,type-query,bindings-json([Value-initial_value]),output-Initial value: initial_value])])])
json([jsonrpc-2.0,id-3,result-json([1-json([status-success,type-query,bindings-json([Value-initial_value]),output-Current value: initial_value])])])
json([jsonrpc-2.0,id-4,result-json([1-json([status-success,type-check_storage,bindings-json([]),output-,widget_html- <script>console.log("Checking localStorage for widget values..."); const widgets = []; for (let i = 0; i < localStorage.length; i++) { const key = localStorage.key(i); if (key && key.startsWith("logtalk_widget_")) { try { const data = JSON.parse(localStorage.getItem(key)); widgets.push(data); console.log("Found widget in localStorage:", data); } catch (e) { console.log("Failed to parse widget data for key:", key); } } } if (widgets.length > 0) { console.log("Total widgets found in localStorage:", widgets.length); } else { console.log("No widgets found in localStorage"); }</script>]),2-json([status-success,type-query,bindings-json([]),output-
=== CHECKING LOCALSTORAGE ===
LocalStorage check completed.])])])
json([jsonrpc-2.0,id-5,result-json([1-json([status-success,type-query,bindings-json([Value-initial_value]),output-Final value: initial_value])])])
json([jsonrpc-2.0,id-6,result-json([1-json([status-error,error-json([code- -4711,message-Failure,data-json([logtalk_message-,output- === Widget Debug Information ===
Widget final_test: Type=text_input, Value=initial_value])])])])])
