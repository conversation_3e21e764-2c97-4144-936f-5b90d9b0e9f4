{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Auto-Sync Widget Test\n", "\n", "Testing widgets that automatically sync values to the server when clicking Update."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Create Auto-Sync Widget"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::create_text_input(auto_sync_test, 'Auto-Sync Test:', 'initial_value')."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Check Initial Value"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::get_widget_value(auto_sync_test, Value),\n", "format('Initial server value: ~w~n', [Value])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Test Auto-Sync\n", "\n", "1. **Type something new** in the text input above (e.g., \"auto_synced_value\")\n", "2. **Click the \"Update\" button**\n", "3. **Watch the status indicator** next to the button:\n", "   - **\"Syncing...\"** (orange) - attempting to sync\n", "   - **\"✅ Synced!\"** (green) - auto-sync worked!\n", "   - **\"⚠️ Manual sync needed\"** (red) - auto-sync failed, use manual method\n", "\n", "The widget will try to automatically sync the value to the server."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Check if Auto-Sync Worked"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::get_widget_value(auto_sync_test, Value),\n", "format('Current server value: ~w~n', [Value])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Manual Sync (if needed)\n", "\n", "If the status shows \"⚠️ Manual sync needed\", use this command with your typed value:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["% Replace 'your_typed_value' with what you actually typed\n", "jupyter::sync_widget(auto_sync_test, 'your_typed_value')."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Final Verification"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::get_widget_value(auto_sync_test, Value),\n", "format('Final server value: ~w~n', [Value])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## How Auto-Sync Works\n", "\n", "The enhanced widget tries multiple approaches when you click \"Update\":\n", "\n", "1. **HTTP Request**: <PERSON><PERSON> to send a GET request to `/execute?code=...`\n", "2. **Status Feedback**: Shows real-time sync status\n", "3. **localStorage Backup**: Always stores value locally as fallback\n", "4. **Graceful Degradation**: Falls back to manual sync if auto-sync fails\n", "\n", "## Expected Results\n", "\n", "### If Auto-Sync Works:\n", "- Status shows \"✅ Synced!\"\n", "- Step 4 shows your typed value\n", "- No manual intervention needed\n", "\n", "### If Auto-Sync Fails:\n", "- Status shows \"⚠️ Manual sync needed\"\n", "- <PERSON><PERSON><PERSON> shows the manual sync command\n", "- Use Step 5 to manually sync\n", "- Step 6 shows the updated value\n", "\n", "## <PERSON><PERSON><PERSON> Console\n", "\n", "Check browser console (F12) for detailed logging:\n", "- \"Auto-syncing value for auto_sync_test: your_value\"\n", "- \"Sync successful\" or \"Auto-sync failed\"\n", "- Manual sync command if needed\n", "\n", "## Benefits\n", "\n", "- ✅ **One-click operation** - no manual sync needed (when it works)\n", "- ✅ **Visual feedback** - clear status indicators\n", "- ✅ **Graceful fallback** - manual sync always available\n", "- ✅ **Reliable backup** - localStorage preserves values\n", "\n", "This provides the best of both worlds: automatic sync when possible, manual sync as backup!"]}], "metadata": {"kernelspec": {"display_name": "Logtalk", "language": "logtalk", "name": "logtalk"}, "language_info": {"codemirror_mode": "prolog", "file_extension": ".lgt", "mimetype": "text/x-logtalk", "name": "logtalk", "pygments_lexer": "prolog", "version": "3.0"}}, "nbformat": 4, "nbformat_minor": 4}