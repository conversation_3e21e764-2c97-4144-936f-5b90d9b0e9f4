{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Embedded Function Test\n", "\n", "Testing widgets with embedded JavaScript functions (no external library dependency)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Create Widget with Embedded Function"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::create_text_input(embedded_test, 'Embedded Function Test:', 'test_value')."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Check Initial Value"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::get_widget_value(embedded_test, Value),\n", "format('Initial value: ~w~n', [Value])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Test Widget Interaction\n", "\n", "**Type something in the widget above.** \n", "\n", "You should see console messages like:\n", "- \"Widget function created for: embedded_test\"\n", "- \"Widget update called: embedded_test, your_typed_value\"\n", "- \"Stored widget data: {...}\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Check localStorage\n", "\n", "Run this in browser console (F12):\n", "\n", "```javascript\n", "// Check what was stored\n", "const data = localStorage.getItem('logtalk_widget_embedded_test');\n", "if (data) {\n", "    console.log('Found widget data:', JSON.parse(data));\n", "} else {\n", "    console.log('No widget data found in localStorage');\n", "}\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Manual Value Sync\n", "\n", "If the value is in localStorage, manually sync it:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["% Check localStorage and manually sync if needed\n", "jupyter_widget_handling::check_localstorage_widgets."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Manual Update Test\n", "\n", "Try this in browser console to manually update the value:\n", "\n", "```javascript\n", "// Manual update test\n", "const data = localStorage.getItem('logtalk_widget_embedded_test');\n", "if (data) {\n", "    const widget = JSON.parse(data);\n", "    console.log('Manual sync for widget:', widget.widgetId, 'value:', widget.value);\n", "    \n", "    // Try to execute the code directly (this won't work but shows the intent)\n", "    console.log('Code to execute:', widget.code);\n", "} else {\n", "    console.log('No widget data to sync');\n", "}\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 7: Check Final Value"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::get_widget_value(embedded_test, Value),\n", "format('Final value: ~w~n', [Value])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Expected Results\n", "\n", "1. **Widget appears** with embedded JavaScript function\n", "2. **<PERSON><PERSON><PERSON> shows** function creation and update messages\n", "3. **localStorage contains** widget data when you type\n", "4. **Values are captured** even if not synced to server\n", "\n", "This proves the widget interaction works - we just need to solve the server communication part.\n", "\n", "## Next Steps\n", "\n", "If this works (localStorage contains data), we can implement:\n", "1. **Polling system** - Server periodically checks localStorage\n", "2. **Manual sync commands** - User runs cells to sync values\n", "3. **Alternative communication** - Find working kernel access method\n", "\n", "The embedded function approach eliminates the \"Can't find variable\" error!"]}], "metadata": {"kernelspec": {"display_name": "Logtalk", "language": "logtalk", "name": "logtalk"}, "language_info": {"codemirror_mode": "prolog", "file_extension": ".lgt", "mimetype": "text/x-logtalk", "name": "logtalk", "version": "3.0"}}, "nbformat": 4, "nbformat_minor": 4}