{"cells": [{"cell_type": "code", "execution_count": 1, "id": "7ecf4f47-a9ea-4685-a0d2-bf19969518c5", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1mVersionData = swi(9,1,1,[])"]}, "metadata": {}, "output_type": "display_data"}], "source": ["current_prolog_flag(version_data, VersionData)."]}, {"cell_type": "code", "execution_count": 2, "id": "19b379ee-af12-4387-82c6-17684eb0ed2a", "metadata": {}, "outputs": [{"data": {"text/plain": ["$VersionData = swi(9,1,1,[])"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["jupyter::print_variable_bindings."]}, {"cell_type": "code", "execution_count": 3, "id": "7123cd9b-94e7-4720-ab6c-3ee703a2c3a9", "metadata": {}, "outputs": [{"data": {"text/plain": ["swi(9,1,1,[])"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mVersionData = swi(9,1,1,[])"]}, "metadata": {}, "output_type": "display_data"}], "source": ["write($VersionData)."]}, {"cell_type": "code", "execution_count": 4, "id": "75732670-e523-4c57-bf0b-dd02708c95eb", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["gnu."]}, {"cell_type": "code", "execution_count": 5, "id": "1ba448f2-c534-4296-95fd-535366b636c9", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1mVD = gprolog(1,5,1,[])"]}, "metadata": {}, "output_type": "display_data"}], "source": ["current_prolog_flag(version_data, VD)."]}, {"cell_type": "code", "execution_count": 6, "id": "fe5f386a-8cfd-4ad9-8e48-73cddd518760", "metadata": {}, "outputs": [{"data": {"text/plain": ["$VD = gprolog(1,5,1,[])"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1myes"]}, "metadata": {}, "output_type": "display_data"}], "source": ["jupyter::print_variable_bindings."]}, {"cell_type": "code", "execution_count": 7, "id": "d102f067-90a6-408e-b4d2-6fe503e4e005", "metadata": {}, "outputs": [{"data": {"text/plain": ["gprolog(1,5,1,[])"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mVD = gprolog(1,5,1,[])"]}, "metadata": {}, "output_type": "display_data"}], "source": ["write($VD)."]}, {"cell_type": "code", "execution_count": 8, "id": "3018695a-4f60-4b85-abcd-a68cf3412e7e", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1myes"]}, "metadata": {}, "output_type": "display_data"}], "source": ["swi."]}, {"cell_type": "code", "execution_count": 9, "id": "39dff8b6-75f8-4d5c-be58-d75efe940091", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["gnu."]}, {"cell_type": "code", "execution_count": 10, "id": "7a691b31-c6ac-4491-956c-697c0b4fe1d9", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1mP = 1,\n", "T = fx"]}, "metadata": {}, "output_type": "display_data"}], "source": ["current_op(P, T, $)."]}, {"cell_type": "code", "execution_count": 11, "id": "03151dd9-b97c-4ec0-bab1-5bdbc828ded8", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1myes"]}, "metadata": {}, "output_type": "display_data"}], "source": ["swi."]}, {"cell_type": "code", "execution_count": 12, "id": "ccbc9616-7125-4552-bc16-2fb4dba84442", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1mP = 1,\n", "T = fx"]}, "metadata": {}, "output_type": "display_data"}], "source": ["current_op(P, T, $)."]}, {"cell_type": "code", "execution_count": 13, "id": "74cb5acd-a4b3-46b3-b188-415076c58827", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["lvm."]}, {"cell_type": "code", "execution_count": 14, "id": "6e68f58e-de0a-4975-9111-a220e53df896", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1mP = 1,\n", "T = fx"]}, "metadata": {}, "output_type": "display_data"}], "source": ["current_op(P, T, $)."]}, {"cell_type": "code", "execution_count": 15, "id": "0767f1e8-5c75-49ab-88be-e56e8e59eacf", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1mVD = lvm(4,5,0,8aa67886f)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["current_prolog_flag(version_data, VD)."]}, {"cell_type": "code", "execution_count": 16, "id": "afda9a24-1243-4577-8a74-2e0ba5cd76e6", "metadata": {}, "outputs": [{"data": {"text/plain": ["$P = 1\n", "$T = fx\n", "$VD = lvm(4,5,0,'8aa67886f')"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["jupyter::print_variable_bindings."]}, {"cell_type": "code", "execution_count": 17, "id": "ca3d15fd-435f-427e-82cc-7d3f2a3ae911", "metadata": {}, "outputs": [{"data": {"text/plain": ["lvm(4,5,0,8aa67886f)"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mVD = lvm(4,5,0,8aa67886f)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["write($VD)."]}, {"cell_type": "code", "execution_count": 18, "id": "93207e12-422d-4ad7-a3f6-716558b70d8a", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["eclipse."]}, {"cell_type": "code", "execution_count": 19, "id": "9b0e84cc-c550-4cc0-9b0a-cb3d25ecfbf1", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1mX = eclipse(7,0,57)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["current_prolog_flag(version_data, X)."]}, {"cell_type": "code", "execution_count": 20, "id": "ec6f17e8-9315-4b40-9bf7-e57a14196f69", "metadata": {}, "outputs": [{"data": {"text/plain": ["$X = eclipse(7,0,57)"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mYes"]}, "metadata": {}, "output_type": "display_data"}], "source": ["jupyter::print_variable_bindings."]}, {"cell_type": "code", "execution_count": 21, "id": "d2b0be68-66a1-4a90-b7ae-ed624973510b", "metadata": {}, "outputs": [{"data": {"text/plain": ["eclipse(7,0,57)"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mX = eclipse(7,0,57)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["write($X)."]}, {"cell_type": "code", "execution_count": 22, "id": "2180c262-4ff5-4312-9a06-590d3c9381f0", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1mP = 1,\n", "T = fx"]}, "metadata": {}, "output_type": "display_data"}], "source": ["current_op(P, T, $)."]}, {"cell_type": "code", "execution_count": 23, "id": "8a58201b-474f-403e-bab1-a50315ae67bd", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1mYes"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sicstus."]}, {"cell_type": "code", "execution_count": 24, "id": "4afafd82-959e-4d6a-858f-03e847f6535c", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1mXYZ = sicstus(4,8,0,3,[])"]}, "metadata": {}, "output_type": "display_data"}], "source": ["current_prolog_flag(version_data, XYZ)."]}, {"cell_type": "code", "execution_count": 25, "id": "8f3125cb-db35-4eab-9af1-3529b373c1a7", "metadata": {}, "outputs": [{"data": {"text/plain": ["$XYZ = sicstus(4,8,0,3,[])"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1myes"]}, "metadata": {}, "output_type": "display_data"}], "source": ["jupyter::print_variable_bindings."]}, {"cell_type": "code", "execution_count": 26, "id": "5983af80-5b4e-4989-bca1-0b1997e918ac", "metadata": {}, "outputs": [{"data": {"text/plain": ["sicstus(4,8,0,3,[])"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mXYZ = sicstus(4,8,0,3,[])"]}, "metadata": {}, "output_type": "display_data"}], "source": ["write($XYZ)."]}, {"cell_type": "code", "execution_count": 27, "id": "5d7e44ca-a7d5-4a82-ab92-521d13521562", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1mP = 1,\n", "T = fx"]}, "metadata": {}, "output_type": "display_data"}], "source": ["current_op(P, T, $)."]}, {"cell_type": "code", "execution_count": null, "id": "381b5fe9-c903-4477-8bed-d335046dd20b", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1;31m!     The Response object is no valid JSON object\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["jupyter::print_sld_tree(member(X,[1,2,3]))."]}, {"cell_type": "code", "execution_count": null, "id": "5603e9df-4875-49d7-aeec-0bf9491efb31", "metadata": {}, "outputs": [], "source": ["true."]}, {"cell_type": "code", "execution_count": 1, "id": "a9efbbb2-0547-413a-885f-898d1bc53d73", "metadata": {}, "outputs": [{"data": {"text/plain": ["jupyter::halt or halt\n", "\n", "    Shuts down the running Prolog process.\n", "\n", "    The next time code is to be executed, a new process is started.\n", "    Everything defined in the database before does not exist anymore.\n", "\n", "    Corresponds to the functionality of halt/0.\n", "    Has the same effect as interrupting or restarting the Jupyter kernel.\n", "\n", "--------------------------------------------------------------------------------\n", "\n", "jupyter::help\n", "\n", "    Outputs the documentation for all predicates from object jupyter.\n", "\n", "--------------------------------------------------------------------------------\n", "\n", "jupyter::print_query_time\n", "\n", "    Prints the latest previous query and its runtime in seconds.\n", "\n", "--------------------------------------------------------------------------------\n", "\n", "jupyter::print_queries(+Ids)\n", "\n", "    Prints previous queries which were executed in requests with IDs in Ids.\n", "\n", "    Any $Var terms might be replaced by the variable's name.\n", "    This is the case if a previous query with ID in Ids contains Var.\n", "    Otherwise, $Var is not replaced.\n", "\n", "--------------------------------------------------------------------------------\n", "\n", "jupyter::print_table(+Goal)\n", "\n", "    Computes all results of the goal Goal with findall/3.\n", "    These are printed in a table.\n", "\n", "    Needs to be the only goal of a query.\n", "\n", "    Example: jupyter::print_table(current_prolog_flag(FlagName, Value)).\n", "\n", "--------------------------------------------------------------------------------\n", "\n", "jupyter::print_table(+ValuesLists, +VariableNames)\n", "\n", "    Prints a table of the values in ValuesLists.\n", "\n", "    ValuesLists is a list of lists of the same length.\n", "    Each list corresponds to one line of the table.\n", "\n", "    VariableNames is used to fill the header of the table.\n", "    If VariableNames=[], capital letters are used.\n", "    Otherwise, VariableNames needs to be a list of ground terms.\n", "    It needs to be of the same length as the values lists.\n", "\n", "    Needs to be the only goal of a query.\n", "\n", "    Can be used with a predicate like findall/3, but not directly.\n", "    Instead, a previous binding can be accessed with a $Var term.\n", "\n", "    Examples:\n", "        jupyter::print_table([[10,100],[20,400],[30,900]], ['X', 'Y']).\n", "        jupyter::print_table($ResultLists, []).\n", "\n", "--------------------------------------------------------------------------------\n", "\n", "jupyter::print_transition_graph(+PredSpec, +FromIndex, +ToIndex, +LabelIndex)\n", "\n", "    Finds all solutions of the predicate with specification PredSpec.\n", "    Prints a graph interpreting the solutions as transitions.\n", "\n", "    PredSpec needs to be of the form PredName/PredArity.\n", "\n", "    FromIndex and ToIndex point to predicate arguments used as nodes.\n", "    LabelIndex points to the argument providing a label for an edge.\n", "    If LabelIndex=0, no label is shown.\n", "\n", "    Needs to be the only goal of a query.\n", "\n", "--------------------------------------------------------------------------------\n", "\n", "jupyter::show_term(+Term)\n", "\n", "    Displays a Prolog term as a graph.\n", "\n", "    Needs to be the only goal of a query.\n", "\n", "--------------------------------------------------------------------------------\n", "\n", "jupyter::print_variable_bindings\n", "\n", "    Prints variable bindings from previous queries.\n", "    For each variable, the latest value it was bound to is shown.\n", "\n", "    The variable value can be accessed with a $Var term by any query.\n", "    In that case, the term is replaced by the value.\n", "    If there is no previous value, an error message is printed.\n", "\n", "--------------------------------------------------------------------------------\n", "\n", "jupyter::retry or retry\n", "\n", "    Causes backtracking of the latest active query.\n", "\n", "    Needs to be the only goal of a query.\n", "\n", "--------------------------------------------------------------------------------\n", "\n", "jupyter::set_prolog_backend(+Backend)\n", "\n", "    Activates the given Prolog backend.\n", "\n", "    Needs to be the only goal of a query.\n", "\n", "--------------------------------------------------------------------------------\n", "\n", "jupyter::trace(+Goal)\n", "\n", "    Prints the trace of the goal Goal.\n", "\n", "    Logtalk code needs to be compiled in debug mode.\n", "    By default, no port is leashed so that no user interaction is requested.\n", "    All previously set breakpoints are still active.\n", "\n", "    Needs to be the only goal of a query in order to work as expected."]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["jupyter::help."]}, {"cell_type": "code", "execution_count": 30, "id": "6e5c7ce0-13b8-4479-b934-b19c251ea72f", "metadata": {}, "outputs": [], "source": ["jupyter::update_completion_data."]}, {"cell_type": "code", "execution_count": 1, "id": "fa097927-0d1f-46b9-af6d-adc02964d87a", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["lvm."]}, {"cell_type": "code", "execution_count": 2, "id": "8e1a2887-c262-4503-b679-8e1a289eb084", "metadata": {}, "outputs": [], "source": ["jupyter::update_completion_data."]}, {"cell_type": "code", "execution_count": 2, "id": "f0ee5912-c312-4705-8db5-541faa87f09b", "metadata": {}, "outputs": [{"data": {"text/plain": ["     Leashed ports: (none)\n", "   Debugger switched on: tracing everything for all objects compiled in debug mode.\n", "   Debu<PERSON> switched off."]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mX = 1"]}, "metadata": {}, "output_type": "display_data"}], "source": ["jupyter::trace(list::member(X, [1,2,3]))."]}, {"cell_type": "code", "execution_count": 3, "id": "32a31fd3-492d-4745-8052-67bc1e9d0ab4", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["set_logtalk_flag(debug, on)."]}, {"cell_type": "code", "execution_count": 4, "id": "0aa9fc7a-0875-4ce2-b5e3-ca89c02a3a07", "metadata": {}, "outputs": [{"data": {"text/plain": ["% [ /Users/<USER>/logtalk/examples/ack/ack.lgt loaded ]\n", "% [ /Users/<USER>/logtalk/examples/ack/loader.lgt loaded ]\n", "% (0 warnings)"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["{ack(loader)}."]}, {"cell_type": "code", "execution_count": 5, "id": "b2d2aacb-fa60-46da-8784-93f265e8f51c", "metadata": {}, "outputs": [{"data": {"text/plain": ["     Leashed ports: (none)\n", "   Debugger switched on: tracing everything for all objects compiled in debug mode.\n", "   Call: (1) ack::ack(1,2,_36416)\n", "   Call: (2) ack(1,2,_36416)\n", "   Rule: (2) ack(1,2,_36416)\n", "   Call: (3) _40120 is 1-1\n", "   Exit: (3) 0 is 1-1\n", "   Call: (4) _40738 is 2-1\n", "   Exit: (4) 1 is 2-1\n", "   Call: (5) ack(1,1,_41360)\n", "   Rule: (5) ack(1,1,_41360)\n", "   Call: (6) _42010 is 1-1\n", "   Exit: (6) 0 is 1-1\n", "   Call: (7) _42628 is 1-1\n", "   Exit: (7) 0 is 1-1\n", "   Call: (8) ack(1,0,_43250)\n", "   Rule: (8) ack(1,0,_43250)\n", "   Call: (9) !\n", "   Exit: (9) !\n", "   Call: (10) _44494 is 1-1\n", "   Exit: (10) 0 is 1-1\n", "   Call: (11) ack(0,1,_43250)\n", "   Rule: (11) ack(0,1,_43250)\n", "   Call: (12) !\n", "   Exit: (12) !\n", "   Call: (13) _43250 is 1+1\n", "   Exit: (13) 2 is 1+1\n", "   Exit: (11) ack(0,1,2)\n", "   Exit: (8) ack(1,0,2)\n", "   Call: (14) ack(0,2,_41360)\n", "   Rule: (14) ack(0,2,_41360)\n", "   Call: (15) !\n", "   Exit: (15) !\n", "   Call: (16) _41360 is 2+1\n", "   Exit: (16) 3 is 2+1\n", "   Exit: (14) ack(0,2,3)\n", "   Exit: (5) ack(1,1,3)\n", "   Call: (17) ack(0,3,_36416)\n", "   Rule: (17) ack(0,3,_36416)\n", "   Call: (18) !\n", "   Exit: (18) !\n", "   Call: (19) _36416 is 3+1\n", "   Exit: (19) 4 is 3+1\n", "   Exit: (17) ack(0,3,4)\n", "   Exit: (2) ack(1,2,4)\n", "   Exit: (1) ack::ack(1,2,4)\n", "   Debu<PERSON> switched off."]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mX = 4"]}, "metadata": {}, "output_type": "display_data"}], "source": ["jupyter::trace(ack::ack(1,2,X))."]}, {"cell_type": "code", "execution_count": null, "id": "4e3b162d-f2b7-466a-b2f2-e549b12d418f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Logtalk", "language": "logtalk", "name": "logtalk_kernel"}, "language_info": {"codemirror_mode": "logtalk", "file_extension": ".lgt", "mimetype": "text/x-logtalk", "name": "Logtalk"}}, "nbformat": 4, "nbformat_minor": 5}