{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Safe Widget Test\n", "\n", "A simple, kernel-safe approach to widget interaction."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Create Simple Widget"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::create_text_input(safe_test, 'Safe Test Widget:', 'initial_value')."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Check Initial Value"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::get_widget_value(safe_test, Value),\n", "format('Initial value: ~w~n', [Value])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Use the Widget\n", "\n", "1. **Type something** in the text input above\n", "2. **Click the \"Update\" button** next to it\n", "3. **You should see an alert** with the captured value\n", "\n", "This proves the widget interaction works without crashing the kernel."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: <PERSON>\n", "\n", "Open browser console (F12) and look for:\n", "- \"Captured value for safe_test: your_typed_value\"\n", "\n", "Also check localStorage:\n", "```javascript\n", "console.log('Widget value:', localStorage.getItem('widget_safe_test'));\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Manual Sync\n", "\n", "After capturing a value, manually sync it to the server.\n", "\n", "**Replace 'your_captured_value' with what you typed:**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter_widget_handling::sync_widget_value(safe_test, 'your_captured_value')."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: <PERSON><PERSON><PERSON> Sync"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::get_widget_value(safe_test, Value),\n", "format('Synced value: ~w~n', [Value])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## How This Works\n", "\n", "This safe approach:\n", "\n", "1. **Creates a simple widget** with minimal JavaScript\n", "2. **Captures values** when you click \"Update\"\n", "3. **Stores in localStorage** (no kernel communication)\n", "4. **Manual sync** to transfer values to server\n", "5. **No complex escaping** that breaks JSON\n", "\n", "## Benefits\n", "\n", "- ✅ **Won't crash the kernel**\n", "- ✅ **Simple and reliable**\n", "- ✅ **Easy to debug**\n", "- ✅ **Works in any environment**\n", "\n", "## Next Steps\n", "\n", "If this works, we can:\n", "1. **Automate the sync** with a polling system\n", "2. **Add more widget types** using the same pattern\n", "3. **Create helper functions** to make sync easier\n", "\n", "This proves the concept works without the complexity that was crashing the kernel!"]}], "metadata": {"kernelspec": {"display_name": "Logtalk", "language": "logtalk", "name": "logtalk"}, "language_info": {"codemirror_mode": "prolog", "file_extension": ".lgt", "name": "logtalk", "pygments_lexer": "prolog", "version": "3.0"}}, "nbformat": 4, "nbformat_minor": 4}