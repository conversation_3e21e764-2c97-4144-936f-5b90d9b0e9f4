{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Find JupyterLab Application Instance\n", "\n", "Since _JUPYTERLAB is just a plugin registry, we need to find the actual application instance."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Create Test Widget"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::create_text_input(app_search_test, 'App Search Test:', 'searching_for_app')."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Comprehensive Application Search\n", "\n", "Run this in browser console (F12):\n", "\n", "```javascript\n", "console.log('=== COMPREHENSIVE JUPYTERLAB APP SEARCH ===');\n", "\n", "// 1. Search all global variables for app-like objects\n", "console.log('\\n1. Searching global variables...');\n", "const globalKeys = Object.keys(window);\n", "const appCandidates = [];\n", "\n", "for (const key of globalKeys) {\n", "    try {\n", "        const obj = window[key];\n", "        if (obj && typeof obj === 'object' && obj.shell) {\n", "            console.log(`Found object with shell: ${key}`, obj);\n", "            appCandidates.push({name: key, obj: obj});\n", "        }\n", "    } catch (e) {\n", "        // Continue\n", "    }\n", "}\n", "\n", "console.log('App candidates:', appCandidates);\n", "\n", "// 2. Search document elements\n", "console.log('\\n2. Searching document elements...');\n", "if (document.body) {\n", "    console.log('document.body keys:', Object.keys(document.body));\n", "    for (const key of Object.keys(document.body)) {\n", "        if (key.includes('jupyter') || key.includes('lab') || key.includes('app')) {\n", "            console.log(`Found potential app property: ${key}`, document.body[key]);\n", "        }\n", "    }\n", "}\n", "\n", "// 3. Search for elements with JupyterLab data\n", "console.log('\\n3. Searching DOM elements...');\n", "const elements = document.querySelectorAll('*');\n", "let foundElements = 0;\n", "for (const el of elements) {\n", "    for (const key of Object.keys(el)) {\n", "        if (key.includes('jupyter') || key.includes('lab')) {\n", "            console.log(`Found element property: ${key}`, el[key]);\n", "            foundElements++;\n", "            if (foundElements > 5) break; // Limit output\n", "        }\n", "    }\n", "    if (foundElements > 5) break;\n", "}\n", "\n", "// 4. Check for event listeners or observers\n", "console.log('\\n4. Checking for MutationObserver or event system...');\n", "if (window.MutationObserver) {\n", "    console.log('MutationObserver available - could monitor for app creation');\n", "}\n", "\n", "// 5. Try to trigger app discovery through events\n", "console.log('\\n5. Dispatching discovery events...');\n", "const discoveryEvent = new CustomEvent('jupyterlab-app-discovery', {\n", "    detail: { searching: true }\n", "});\n", "document.dispatchEvent(discoveryEvent);\n", "window.dispatchEvent(discoveryEvent);\n", "\n", "console.log('=== END APP SEARCH ===');\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Plugin-Based Approach\n", "\n", "Since we have the plugin registry, let's try to use it:\n", "\n", "```javascript\n", "console.log('=== PLUGIN-BASED APPROACH ===');\n", "\n", "// Check if any plugins expose the application\n", "const plugins = window._JUPYTERLAB;\n", "console.log('Available plugins:', Object.keys(plugins));\n", "\n", "for (const pluginName of Object.keys(plugins)) {\n", "    const plugin = plugins[pluginName];\n", "    console.log(`\\nPlugin ${pluginName}:`, plugin);\n", "    \n", "    if (plugin && typeof plugin === 'object') {\n", "        const keys = Object.keys(plugin);\n", "        console.log(`  Keys: ${keys.join(', ')}`);\n", "        \n", "        // Look for app-like properties\n", "        for (const key of keys) {\n", "            if (key.includes('app') || key.includes('shell') || key.includes('kernel')) {\n", "                console.log(`  Found interesting property: ${key}`, plugin[key]);\n", "            }\n", "        }\n", "    }\n", "}\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Alternative Communication Method\n", "\n", "If we can't find the app directly, let's try a different approach:\n", "\n", "```javascript\n", "console.log('=== ALTERNATIVE COMMUNICATION ===');\n", "\n", "// Method 1: Try to find any execute function\n", "console.log('\\n1. Searching for execute functions...');\n", "const executeFunctions = [];\n", "for (const key in window) {\n", "    if (typeof window[key] === 'function') {\n", "        const funcStr = window[key].toString();\n", "        if (funcStr.includes('execute') || funcStr.includes('kernel')) {\n", "            executeFunctions.push(key);\n", "            console.log(`Found function: ${key}`);\n", "        }\n", "    }\n", "}\n", "\n", "// Method 2: Try iframe communication\n", "console.log('\\n2. Trying iframe communication...');\n", "if (window.parent !== window) {\n", "    console.log('In iframe - trying parent communication');\n", "    window.parent.postMessage({\n", "        type: 'jupyter_kernel_request',\n", "        code: \"write('Parent communication test'), nl.\"\n", "    }, '*');\n", "}\n", "\n", "// Method 3: Try to create a custom protocol\n", "console.log('\\n3. Setting up custom protocol...');\n", "window.jupyterLabWidgetProtocol = {\n", "    executeCode: function(code) {\n", "        console.log('Custom protocol execute request:', code);\n", "        // This would need to be implemented by JupyterLab\n", "        return false;\n", "    }\n", "};\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Test Enhanced Widget Update"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["% Try the widget update with enhanced search\n", "jupyter::get_widget_value(app_search_test, Value),\n", "format('Current value: ~w~n', [Value])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Manual Widget Update Test\n", "\n", "After running the searches above, try this:\n", "\n", "```javascript\n", "// Test the enhanced widget update function\n", "console.log('=== TESTING ENHANCED WIDGET UPDATE ===');\n", "if (typeof updateLogtalkWidget === 'function') {\n", "    updateLogtalkWidget('app_search_test', 'enhanced_search_result');\n", "} else {\n", "    console.log('updateLogtalkWidget not available');\n", "}\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Instructions\n", "\n", "1. **Run all cells above**\n", "2. **Execute each console script** in order\n", "3. **Look for any objects with 'shell' property**\n", "4. **Check if any plugins expose the application**\n", "5. **Try the enhanced widget update**\n", "6. **Report what you find**\n", "\n", "This comprehensive search should reveal how to access the JupyterLab application in your specific environment."]}], "metadata": {"kernelspec": {"display_name": "Logtalk", "language": "logtalk", "name": "logtalk"}, "language_info": {"codemirror_mode": "prolog", "file_extension": ".lgt", "mimetype": "text/x-logtalk", "name": "logtalk", "pygments_lexer": "prolog", "version": "3.0"}}, "nbformat": 4, "nbformat_minor": 4}