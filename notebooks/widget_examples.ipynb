<script>
console.log('=== Kernel Test ===');
if (typeof Jupyter !== 'undefined' && Jupyter.notebook && Jupyter.notebook.kernel) {
    console.log('✅ Jupyter kernel found!');
    
    // Test direct execution
    Jupyter.notebook.kernel.execute('write("Direct kernel test successful!"), nl.', {
        silent: false,
        store_history: false
    });
} else {
    console.log('❌ No Jupyter kernel found');
}
</script>

jupyter::create_text_input(test_widget, 'Test Input:', 'initial_value').

%versions

jupyter::create_text_input(name_input, 'Enter your name:', '<PERSON>').

jupyter::get_widget_value(name_input, Name).

jupyter::create_number_input(age_input, 'Enter your age:', 25, [min(0), max(120), step(1)]).

jupyter::create_slider(temperature_slider, 'Temperature (°C)', -10, 40, 20).

jupyter::create_dropdown(color_select, 'Choose a color:', [red, green, blue, yellow, purple]).

jupyter::create_checkbox(newsletter_checkbox, 'Subscribe to newsletter', false).

jupyter::create_button(action_button, 'Click Me!').



jupyter::create_input_form(contact_form, [
    text_field(name, 'Full Name:', ''),
    email_field(email, 'Email Address:', ''),
    number_field(age, 'Age:', 0),
    select_field(country, 'Country:', [usa, canada, uk, germany, france], usa),
    textarea_field(message, 'Message:', '', 4),
    checkbox_field(newsletter, 'Subscribe to newsletter', false)
], [
    title('Contact Information'),
    submit_label('Submit Form'),
    cancel_label('Cancel')
]).

jupyter::get_form_data(contact_form, ContactData).

% Define a predicate that uses widget values
jupyter::get_widget_value(name_input, Name),
jupyter::get_widget_value(age_input, Age),
jupyter::get_widget_value(color_select, Color),
format('User ~w is ~w years old and likes ~w~n', [Name, Age, Color]).

process_user_input.

% Create a survey form
jupyter::create_input_form(survey_form, [
    text_field(participant_id, 'Participant ID:', ''),
    select_field(experience, 'Programming Experience:', [beginner, intermediate, advanced], beginner),
    number_field(years_coding, 'Years of Coding:', 0),
    select_field(favorite_language, 'Favorite Language:', [python, java, javascript, prolog, logtalk], python),
    textarea_field(comments, 'Additional Comments:', '', 3)
], [
    title('Programming Survey'),
    submit_label('Submit Survey')
]).

% Process survey results
jupyter::get_form_data(survey_form, SurveyData).