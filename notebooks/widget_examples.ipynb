{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Logtalk Widget Examples\n", "\n", "This notebook demonstrates the data input widget capabilities of the Logtalk Jupyter kernel.\n", "\n", "## HTML/JavaScript Widgets\n", "\n", "The Logtalk kernel supports interactive HTML/JavaScript widgets for data input."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["Logtalk 3.93.0-b01\n", "SWI-Prolog 9.3.24\n", "Logtalk Jupyter kernel 0.32.0-beta"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%versions"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Text Input Widget\n", "\n", "Create a simple text input widget:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/html": ["\n", "        <div class=\"logtalk-widget-container\">\n", "            <div class=\"logtalk-widget\" id=\"container_name_input\"><label for=\"name_input\">Enter your name:</label><br><input type=\"text\" id=\"name_input\" value=\"John Doe\" onchange=\"updateLogtalkWidget('name_input', this.value)\" style=\"margin: 5px; padding: 5px; border: 1px solid #ccc; border-radius: 3px;\"></div><script>setTimeout(function() {  if (typeof autoRegisterWidget === \"function\") {    autoRegisterWidget(\"name_input\", \"text_input\", \"John Doe\");  }}, 100);</script>\n", "        </div>\n", "        <script>\n", "            // Ensure widget library is loaded only once\n", "            if (typeof LogtalkWidgets === 'undefined') {\n", "                /**\n", " * Logtalk Jupyter <PERSON> Widget Support\n", " * JavaScript communication layer for HTML/JavaScript widgets\n", " */\n", "\n", "// Global widget state management\n", "window.LogtalkWidgets = {\n", "    widgets: new Map(),\n", "    kernel: null,\n", "    \n", "    // Initialize widget system\n", "    init: function() {\n", "        // Get reference to <PERSON><PERSON><PERSON> kernel\n", "        if (typeof Jupyter !== 'undefined' && Jupyter.notebook) {\n", "            this.kernel = Jupyter.notebook.kernel;\n", "        } else if (typeof window.parent !== 'undefined' && window.parent.Jupyter) {\n", "            this.kernel = window.parent.Jupyter.notebook.kernel;\n", "        }\n", "        \n", "        console.log('Logtalk Widgets initialized');\n", "    },\n", "    \n", "    // Register a widget\n", "    registerWidget: function(widgetId, type, initialValue) {\n", "        this.widgets.set(widgetId, {\n", "            type: type,\n", "            value: initialValue,\n", "            element: document.getElementById(widgetId)\n", "        });\n", "    },\n", "    \n", "    // Update widget value and notify kernel\n", "    updateWidget: function(widgetId, value) {\n", "        if (this.widgets.has(widgetId)) {\n", "            const widget = this.widgets.get(widgetId);\n", "            widget.value = value;\n", "            \n", "            // Send update to kernel\n", "            this.sendWidgetUpdate(widgetId, value);\n", "        }\n", "    },\n", "    \n", "    // Send widget update to Logtalk kernel\n", "    sendWidgetUpdate: function(widgetId, value) {\n", "        if (!this.kernel) {\n", "            console.warn('Kernel not available for widget update');\n", "            return;\n", "        }\n", "\n", "        // Properly escape the value for <PERSON><PERSON><PERSON>\n", "        let escapedValue;\n", "        if (typeof value === 'string') {\n", "            // Escape single quotes and backslashes for Logtalk string literals\n", "            escapedValue = value.replace(/\\\\/g, '\\\\\\\\').replace(/'/g, \"\\\\'\");\n", "            escapedValue = `'${escapedValue}'`;\n", "        } else if (typeof value === 'boolean') {\n", "            escapedValue = value ? 'true' : 'false';\n", "        } else {\n", "            escapedValue = String(value);\n", "        }\n", "\n", "        const code = `jupyter_widget_handling::set_widget_value('${widgetId}', ${escapedValue}).`;\n", "\n", "        console.log('Sending widget update:', code);\n", "\n", "        this.kernel.execute(code, {\n", "            silent: true,\n", "            store_history: false,\n", "            user_expressions: {},\n", "            allow_stdin: false,\n", "            stop_on_error: false\n", "        });\n", "    },\n", "    \n", "    // Get widget value\n", "    getWidgetValue: function(widgetId) {\n", "        if (this.widgets.has(widgetId)) {\n", "            return this.widgets.get(widgetId).value;\n", "        }\n", "        return null;\n", "    },\n", "    \n", "    // Set widget value from kernel\n", "    setWidgetValue: function(widgetId, value) {\n", "        if (this.widgets.has(widgetId)) {\n", "            const widget = this.widgets.get(widgetId);\n", "            widget.value = value;\n", "            \n", "            // Update DOM element\n", "            const element = widget.element;\n", "            if (element) {\n", "                switch (widget.type) {\n", "                    case 'text_input':\n", "                    case 'number_input':\n", "                        element.value = value;\n", "                        break;\n", "                    case 'slider':\n", "                        element.value = value;\n", "                        // Update display value\n", "                        const valueDisplay = document.getElementById(widgetId + '_value');\n", "                        if (valueDisplay) {\n", "                            valueDisplay.textContent = value;\n", "                        }\n", "                        break;\n", "                    case 'dropdown':\n", "                        element.value = value;\n", "                        break;\n", "                    case 'checkbox':\n", "                        element.checked = (value === true || value === 'true');\n", "                        break;\n", "                }\n", "            }\n", "        }\n", "    },\n", "    \n", "    // Remove widget\n", "    removeWidget: function(widgetId) {\n", "        if (this.widgets.has(widgetId)) {\n", "            this.widgets.delete(widgetId);\n", "            \n", "            // Remove DOM element\n", "            const container = document.getElementById('container_' + widgetId);\n", "            if (container) {\n", "                container.remove();\n", "            }\n", "        }\n", "    },\n", "    \n", "    // Clear all widgets\n", "    clearAllWidgets: function() {\n", "        this.widgets.forEach((widget, widgetId) => {\n", "            this.removeWidget(widgetId);\n", "        });\n", "        this.widgets.clear();\n", "    },\n", "\n", "    // Debug function to check widget state\n", "    debugWidgets: function() {\n", "        console.log('Registered widgets:', this.widgets);\n", "        console.log('Kernel available:', !!this.kernel);\n", "        this.widgets.forEach((widget, widgetId) => {\n", "            console.log(`Widget ${widgetId}:`, {\n", "                type: widget.type,\n", "                value: widget.value,\n", "                element: widget.element,\n", "                elementValue: widget.element ? widget.element.value : 'N/A'\n", "            });\n", "        });\n", "    }\n", "};\n", "\n", "// Global function for widget updates (called from HTML)\n", "function updateLogtalkWidget(widgetId, value) {\n", "    LogtalkWidgets.updateWidget(widgetId, value);\n", "}\n", "\n", "// Auto-register widgets when they are created\n", "function autoRegisterWidget(widgetId, type, initialValue) {\n", "    LogtalkWidgets.registerWidget(widgetId, type, initialValue);\n", "}\n", "\n", "// Initialize when DOM is ready\n", "if (document.readyState === 'loading') {\n", "    document.addEventListener('DOMContentLoaded', function() {\n", "        LogtalkWidgets.init();\n", "    });\n", "} else {\n", "    LogtalkWidgets.init();\n", "}\n", "\n", "// Initialize when <PERSON><PERSON><PERSON> is ready (for notebook environment)\n", "if (typeof Jupyter !== 'undefined') {\n", "    Jupyter.notebook.events.on('kernel_ready.Kernel', function() {\n", "        LogtalkWidgets.init();\n", "    });\n", "}\n", "\n", "// CSS styles for widgets\n", "const widgetStyles = `\n", "<style>\n", ".logtalk-widget {\n", "    margin: 10px 0;\n", "    padding: 10px;\n", "    border: 1px solid #e0e0e0;\n", "    border-radius: 5px;\n", "    background-color: #fafafa;\n", "    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n", "}\n", "\n", ".logtalk-widget label {\n", "    font-weight: 500;\n", "    color: #333;\n", "    margin-bottom: 5px;\n", "    display: inline-block;\n", "}\n", "\n", ".logtalk-widget input[type=\"text\"],\n", ".logtalk-widget input[type=\"number\"],\n", ".logtalk-widget select {\n", "    width: 200px;\n", "    padding: 6px 10px;\n", "    border: 1px solid #ccc;\n", "    border-radius: 4px;\n", "    font-size: 14px;\n", "    transition: border-color 0.3s ease;\n", "}\n", "\n", ".logtalk-widget input[type=\"text\"]:focus,\n", ".logtalk-widget input[type=\"number\"]:focus,\n", ".logtalk-widget select:focus {\n", "    outline: none;\n", "    border-color: #007cba;\n", "    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2);\n", "}\n", "\n", ".logtalk-widget input[type=\"range\"] {\n", "    width: 200px;\n", "    margin: 5px 0;\n", "}\n", "\n", ".logtalk-widget input[type=\"checkbox\"] {\n", "    margin-right: 8px;\n", "    transform: scale(1.2);\n", "}\n", "\n", ".logtalk-widget button {\n", "    background-color: #007cba;\n", "    color: white;\n", "    border: none;\n", "    padding: 8px 16px;\n", "    border-radius: 4px;\n", "    cursor: pointer;\n", "    font-size: 14px;\n", "    transition: background-color 0.3s ease;\n", "}\n", "\n", ".logtalk-widget button:hover {\n", "    background-color: #005a87;\n", "}\n", "\n", ".logtalk-widget button:active {\n", "    background-color: #004a73;\n", "}\n", "</style>\n", "`;\n", "\n", "// Inject CSS styles\n", "if (document.head) {\n", "    document.head.insertAdjacentHTML('beforeend', widgetStyles);\n", "}\n", "\n", "                console.log('Logtalk widget library loaded');\n", "            } else {\n", "                console.log('Logtalk widget library already available');\n", "            }\n", "\n", "            // Ensure kernel reference is updated\n", "            if (typeof LogtalkWidgets !== 'undefined') {\n", "                LogtalkWidgets.init();\n", "            }\n", "        </script>\n", "        "], "text/plain": ["Logtalk Widget"]}, "metadata": {"text/html": {"isolated": false}}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["jupyter::create_text_input(name_input, 'Enter your name:', '<PERSON>')."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Get the value from the widget:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mName = '<PERSON>'"]}, "metadata": {}, "output_type": "display_data"}], "source": ["jupyter::get_widget_value(name_input, Name)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Number Input Widget\n", "\n", "Create a number input with constraints:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::create_number_input(age_input, 'Enter your age:', 25, [min(0), max(120), step(1)])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Slider Widget\n", "\n", "Create a slider for selecting values in a range:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::create_slider(temperature_slider, 'Temperature (°C)', -10, 40, 20)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Dropdown Widget\n", "\n", "Create a dropdown selection:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::create_dropdown(color_select, 'Choose a color:', [red, green, blue, yellow, purple])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Checkbox Widget\n", "\n", "Create a checkbox for boolean input:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::create_checkbox(newsletter_checkbox, 'Subscribe to newsletter', false)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON> Widget\n", "\n", "Create a clickable button:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["jupyter::create_button(action_button, 'Click Me!')."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Form-Based Input System\n", "\n", "For more complex data collection, use the form-based input system:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Simple Contact Form\n", "\n", "Create a form with multiple field types:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 4, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/html": ["\n", "        <div class=\"logtalk-widget-container\">\n", "            <div class=\"logtalk-form\" id=\"contact_form_container\"><form id=\"contact_form\" onsubmit=\"submitLogtalkForm('contact_form'); return false;\"><h3>Contact Information</h3><div class=\"form-field\"><label for=\"name\">Full Name:</label><input type=\"text\" id=\"name\" name=\"name\" value=\"\"></div><div class=\"form-field\"><label for=\"email\">Email Address:</label><input type=\"email\" id=\"email\" name=\"email\" value=\"\"></div><div class=\"form-field\"><label for=\"age\">Age:</label><input type=\"number\" id=\"age\" name=\"age\" value=\"0\"></div><div class=\"form-field\"><label for=\"country\">Country:</label><select id=\"country\" name=\"country\"><option value=\"usa\" selected>usa</option><option value=\"canada\" >canada</option><option value=\"uk\" >uk</option><option value=\"germany\" >germany</option><option value=\"france\" >france</option></select></div><div class=\"form-field\"><label for=\"message\">Message:</label><textarea id=\"message\" name=\"message\" rows=\"4\"></textarea></div><div class=\"form-field\"><input type=\"checkbox\" id=\"newsletter\" name=\"newsletter\" value=\"true\" ><label for=\"newsletter\">Subscribe to newsletter</label></div><div class=\"form-buttons\"><button type=\"submit\" class=\"submit-btn\">Submit Form</button><button type=\"button\" class=\"cancel-btn\" onclick=\"cancelLogtalkForm('contact_form')\">Cancel</button></div></form></div><script>function submitLogtalkForm(formId) {  const form = document.getElementById(formId);  const formData = new FormData(form);  const data = {};  for (let [key, value] of formData.entries()) {    data[key] = value;  }  const code = `jupyter_form_handling::update_form_data('${formId}', ${JSON.stringify(data)}).`;  if (typeof Jupyter !== \"undefined\" && Jupyter.notebook && Jupyter.notebook.kernel) {    Jupyter.notebook.kernel.execute(code, {silent: true, store_history: false});  }  document.getElementById(formId + \"_container\").style.display = \"none\";}function cancelLogtalkForm(formId) {  document.getElementById(formId + \"_container\").style.display = \"none\";}</script><style>.logtalk-form {  max-width: 500px;  margin: 20px 0;  padding: 20px;  border: 1px solid #ddd;  border-radius: 8px;  background-color: #f9f9f9;  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif;}.logtalk-form h3 {  margin-top: 0;  color: #333;}.form-field {  margin-bottom: 15px;}.form-field label {  display: block;  margin-bottom: 5px;  font-weight: 500;  color: #555;}.form-field input, .form-field select, .form-field textarea {  width: 100%;  padding: 8px 12px;  border: 1px solid #ccc;  border-radius: 4px;  font-size: 14px;  box-sizing: border-box;}.form-field input:focus, .form-field select:focus, .form-field textarea:focus {  outline: none;  border-color: #007cba;  box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2);}.form-buttons {  margin-top: 20px;  text-align: right;}.form-buttons button {  margin-left: 10px;  padding: 10px 20px;  border: none;  border-radius: 4px;  cursor: pointer;  font-size: 14px;}.submit-btn {  background-color: #007cba;  color: white;}.submit-btn:hover {  background-color: #005a87;}.cancel-btn {  background-color: #6c757d;  color: white;}.cancel-btn:hover {  background-color: #545b62;}</style>\n", "        </div>\n", "        <script>\n", "            // Ensure widget library is loaded only once\n", "            if (typeof LogtalkWidgets === 'undefined') {\n", "                /**\n", " * Logtalk Jupyter <PERSON> Widget Support\n", " * JavaScript communication layer for HTML/JavaScript widgets\n", " */\n", "\n", "// Global widget state management\n", "window.LogtalkWidgets = {\n", "    widgets: new Map(),\n", "    kernel: null,\n", "    \n", "    // Initialize widget system\n", "    init: function() {\n", "        // Get reference to <PERSON><PERSON><PERSON> kernel\n", "        if (typeof Jupyter !== 'undefined' && Jupyter.notebook) {\n", "            this.kernel = Jupyter.notebook.kernel;\n", "        } else if (typeof window.parent !== 'undefined' && window.parent.Jupyter) {\n", "            this.kernel = window.parent.Jupyter.notebook.kernel;\n", "        }\n", "        \n", "        console.log('Logtalk Widgets initialized');\n", "    },\n", "    \n", "    // Register a widget\n", "    registerWidget: function(widgetId, type, initialValue) {\n", "        this.widgets.set(widgetId, {\n", "            type: type,\n", "            value: initialValue,\n", "            element: document.getElementById(widgetId)\n", "        });\n", "    },\n", "    \n", "    // Update widget value and notify kernel\n", "    updateWidget: function(widgetId, value) {\n", "        if (this.widgets.has(widgetId)) {\n", "            const widget = this.widgets.get(widgetId);\n", "            widget.value = value;\n", "            \n", "            // Send update to kernel\n", "            this.sendWidgetUpdate(widgetId, value);\n", "        }\n", "    },\n", "    \n", "    // Send widget update to Logtalk kernel\n", "    sendWidgetUpdate: function(widgetId, value) {\n", "        if (!this.kernel) {\n", "            console.warn('Kernel not available for widget update');\n", "            return;\n", "        }\n", "\n", "        // Properly escape the value for <PERSON><PERSON><PERSON>\n", "        let escapedValue;\n", "        if (typeof value === 'string') {\n", "            // Escape single quotes and backslashes for Logtalk string literals\n", "            escapedValue = value.replace(/\\\\/g, '\\\\\\\\').replace(/'/g, \"\\\\'\");\n", "            escapedValue = `'${escapedValue}'`;\n", "        } else if (typeof value === 'boolean') {\n", "            escapedValue = value ? 'true' : 'false';\n", "        } else {\n", "            escapedValue = String(value);\n", "        }\n", "\n", "        const code = `jupyter_widget_handling::set_widget_value('${widgetId}', ${escapedValue}).`;\n", "\n", "        console.log('Sending widget update:', code);\n", "\n", "        this.kernel.execute(code, {\n", "            silent: true,\n", "            store_history: false,\n", "            user_expressions: {},\n", "            allow_stdin: false,\n", "            stop_on_error: false\n", "        });\n", "    },\n", "    \n", "    // Get widget value\n", "    getWidgetValue: function(widgetId) {\n", "        if (this.widgets.has(widgetId)) {\n", "            return this.widgets.get(widgetId).value;\n", "        }\n", "        return null;\n", "    },\n", "    \n", "    // Set widget value from kernel\n", "    setWidgetValue: function(widgetId, value) {\n", "        if (this.widgets.has(widgetId)) {\n", "            const widget = this.widgets.get(widgetId);\n", "            widget.value = value;\n", "            \n", "            // Update DOM element\n", "            const element = widget.element;\n", "            if (element) {\n", "                switch (widget.type) {\n", "                    case 'text_input':\n", "                    case 'number_input':\n", "                        element.value = value;\n", "                        break;\n", "                    case 'slider':\n", "                        element.value = value;\n", "                        // Update display value\n", "                        const valueDisplay = document.getElementById(widgetId + '_value');\n", "                        if (valueDisplay) {\n", "                            valueDisplay.textContent = value;\n", "                        }\n", "                        break;\n", "                    case 'dropdown':\n", "                        element.value = value;\n", "                        break;\n", "                    case 'checkbox':\n", "                        element.checked = (value === true || value === 'true');\n", "                        break;\n", "                }\n", "            }\n", "        }\n", "    },\n", "    \n", "    // Remove widget\n", "    removeWidget: function(widgetId) {\n", "        if (this.widgets.has(widgetId)) {\n", "            this.widgets.delete(widgetId);\n", "            \n", "            // Remove DOM element\n", "            const container = document.getElementById('container_' + widgetId);\n", "            if (container) {\n", "                container.remove();\n", "            }\n", "        }\n", "    },\n", "    \n", "    // Clear all widgets\n", "    clearAllWidgets: function() {\n", "        this.widgets.forEach((widget, widgetId) => {\n", "            this.removeWidget(widgetId);\n", "        });\n", "        this.widgets.clear();\n", "    },\n", "\n", "    // Debug function to check widget state\n", "    debugWidgets: function() {\n", "        console.log('Registered widgets:', this.widgets);\n", "        console.log('Kernel available:', !!this.kernel);\n", "        this.widgets.forEach((widget, widgetId) => {\n", "            console.log(`Widget ${widgetId}:`, {\n", "                type: widget.type,\n", "                value: widget.value,\n", "                element: widget.element,\n", "                elementValue: widget.element ? widget.element.value : 'N/A'\n", "            });\n", "        });\n", "    }\n", "};\n", "\n", "// Global function for widget updates (called from HTML)\n", "function updateLogtalkWidget(widgetId, value) {\n", "    LogtalkWidgets.updateWidget(widgetId, value);\n", "}\n", "\n", "// Auto-register widgets when they are created\n", "function autoRegisterWidget(widgetId, type, initialValue) {\n", "    LogtalkWidgets.registerWidget(widgetId, type, initialValue);\n", "}\n", "\n", "// Initialize when DOM is ready\n", "if (document.readyState === 'loading') {\n", "    document.addEventListener('DOMContentLoaded', function() {\n", "        LogtalkWidgets.init();\n", "    });\n", "} else {\n", "    LogtalkWidgets.init();\n", "}\n", "\n", "// Initialize when <PERSON><PERSON><PERSON> is ready (for notebook environment)\n", "if (typeof Jupyter !== 'undefined') {\n", "    Jupyter.notebook.events.on('kernel_ready.Kernel', function() {\n", "        LogtalkWidgets.init();\n", "    });\n", "}\n", "\n", "// CSS styles for widgets\n", "const widgetStyles = `\n", "<style>\n", ".logtalk-widget {\n", "    margin: 10px 0;\n", "    padding: 10px;\n", "    border: 1px solid #e0e0e0;\n", "    border-radius: 5px;\n", "    background-color: #fafafa;\n", "    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n", "}\n", "\n", ".logtalk-widget label {\n", "    font-weight: 500;\n", "    color: #333;\n", "    margin-bottom: 5px;\n", "    display: inline-block;\n", "}\n", "\n", ".logtalk-widget input[type=\"text\"],\n", ".logtalk-widget input[type=\"number\"],\n", ".logtalk-widget select {\n", "    width: 200px;\n", "    padding: 6px 10px;\n", "    border: 1px solid #ccc;\n", "    border-radius: 4px;\n", "    font-size: 14px;\n", "    transition: border-color 0.3s ease;\n", "}\n", "\n", ".logtalk-widget input[type=\"text\"]:focus,\n", ".logtalk-widget input[type=\"number\"]:focus,\n", ".logtalk-widget select:focus {\n", "    outline: none;\n", "    border-color: #007cba;\n", "    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2);\n", "}\n", "\n", ".logtalk-widget input[type=\"range\"] {\n", "    width: 200px;\n", "    margin: 5px 0;\n", "}\n", "\n", ".logtalk-widget input[type=\"checkbox\"] {\n", "    margin-right: 8px;\n", "    transform: scale(1.2);\n", "}\n", "\n", ".logtalk-widget button {\n", "    background-color: #007cba;\n", "    color: white;\n", "    border: none;\n", "    padding: 8px 16px;\n", "    border-radius: 4px;\n", "    cursor: pointer;\n", "    font-size: 14px;\n", "    transition: background-color 0.3s ease;\n", "}\n", "\n", ".logtalk-widget button:hover {\n", "    background-color: #005a87;\n", "}\n", "\n", ".logtalk-widget button:active {\n", "    background-color: #004a73;\n", "}\n", "</style>\n", "`;\n", "\n", "// Inject CSS styles\n", "if (document.head) {\n", "    document.head.insertAdjacentHTML('beforeend', widgetStyles);\n", "}\n", "\n", "                console.log('Logtalk widget library loaded');\n", "            } else {\n", "                console.log('Logtalk widget library already available');\n", "            }\n", "\n", "            // Ensure kernel reference is updated\n", "            if (typeof LogtalkWidgets !== 'undefined') {\n", "                LogtalkWidgets.init();\n", "            }\n", "        </script>\n", "        "], "text/plain": ["Logtalk Widget"]}, "metadata": {"text/html": {"isolated": false}}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["jupyter::create_input_form(contact_form, [\n", "    text_field(name, 'Full Name:', ''),\n", "    email_field(email, 'Email Address:', ''),\n", "    number_field(age, 'Age:', 0),\n", "    select_field(country, 'Country:', [usa, canada, uk, germany, france], usa),\n", "    textarea_field(message, 'Message:', '', 4),\n", "    checkbox_field(newsletter, 'Subscribe to newsletter', false)\n", "], [\n", "    title('Contact Information'),\n", "    submit_label('Submit Form'),\n", "    cancel_label('Cancel')\n", "])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Retrieve the form data after submission:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mContactData = []"]}, "metadata": {}, "output_type": "display_data"}], "source": ["jupyter::get_form_data(contact_form, ContactData)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Processing Widget Data\n", "\n", "You can use widget values in Logtalk predicates:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["% Define a predicate that uses widget values\n", "jupyter::get_widget_value(name_input, Name),\n", "jupyter::get_widget_value(age_input, Age),\n", "jupyter::get_widget_value(color_select, Color),\n", "format('User ~w is ~w years old and likes ~w~n', [Name, Age, Color])."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["process_user_input."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Interactive Data Collection\n", "\n", "Create a more complex example that collects survey data:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["% Create a survey form\n", "jupyter::create_input_form(survey_form, [\n", "    text_field(participant_id, 'Participant ID:', ''),\n", "    select_field(experience, 'Programming Experience:', [beginner, intermediate, advanced], beginner),\n", "    number_field(years_coding, 'Years of Coding:', 0),\n", "    select_field(favorite_language, 'Favorite Language:', [python, java, javascript, prolog, logtalk], python),\n", "    textarea_field(comments, 'Additional Comments:', '', 3)\n", "], [\n", "    title('Programming Survey'),\n", "    submit_label('Submit Survey')\n", "])."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": ["% Process survey results\n", "jupyter::get_form_data(survey_form, SurveyData)."]}], "metadata": {"kernelspec": {"display_name": "Logtalk", "language": "logtalk", "name": "logtalk_kernel"}, "language_info": {"codemirror_mode": "logtalk", "file_extension": ".lgt", "mimetype": "text/x-logtalk", "name": "Logtalk"}}, "nbformat": 4, "nbformat_minor": 4}