{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Kernel Discovery Test\n", "\n", "This will help us find how to access the kernel in your specific JupyterLab environment."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Create Test Widget"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::create_text_input(discovery_test, 'Test Widget:', 'initial_value')."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Check Initial Value"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::get_widget_value(discovery_test, Value),\n", "format('Initial value: ~w~n', [Value])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Manual Browser Console Tests\n", "\n", "Open browser console (F12) and run these tests one by one:\n", "\n", "### Test 1: Environment Discovery\n", "```javascript\n", "// Check what's available globally\n", "console.log('=== Global Object Search ===');\n", "const globalKeys = Object.keys(window);\n", "const jupyterRelated = globalKeys.filter(key => \n", "    key.toLowerCase().includes('jupyter') || \n", "    key.toLowerCase().includes('kernel') ||\n", "    key.toLowerCase().includes('notebook')\n", ");\n", "console.log('Jupyter-related globals:', jupyterRelated);\n", "\n", "// Check for RequireJS\n", "console.log('RequireJS available:', typeof window.require);\n", "console.log('AMD define available:', typeof window.define);\n", "```\n", "\n", "### Test 2: Function Search\n", "```javascript\n", "// Look for any execute functions\n", "console.log('=== Function Search ===');\n", "const executeFunctions = [];\n", "for (let key in window) {\n", "    if (typeof window[key] === 'function') {\n", "        if (key.toLowerCase().includes('execute') || \n", "            key.toLowerCase().includes('kernel') ||\n", "            key.toLowerCase().includes('run')) {\n", "            executeFunctions.push(key);\n", "        }\n", "    }\n", "}\n", "console.log('Potential execute functions:', executeFunctions);\n", "```\n", "\n", "### Test 3: Document Search\n", "```javascript\n", "// Look for kernel info in document\n", "console.log('=== Document Search ===');\n", "const scripts = document.querySelectorAll('script');\n", "let kernelScripts = 0;\n", "for (let script of scripts) {\n", "    if (script.textContent && \n", "        (script.textContent.includes('kernel') || \n", "         script.textContent.includes('jupyter'))) {\n", "        kernelScripts++;\n", "    }\n", "}\n", "console.log('<PERSON><PERSON><PERSON> mentioning kernel/jupyter:', kernelScripts);\n", "```\n", "\n", "### Test 4: Widget Update Test\n", "```javascript\n", "// Try the enhanced widget update\n", "console.log('=== Widget Update Test ===');\n", "if (typeof updateLogtalkWidget === 'function') {\n", "    updateLogtalkWidget('discovery_test', 'browser_console_update');\n", "} else {\n", "    console.log('updateLogtalkWidget function not available');\n", "}\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Check for Updates"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::get_widget_value(discovery_test, Value),\n", "format('Value after console tests: ~w~n', [Value])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Alternative Communication Test\n", "\n", "If the above doesn't work, try this in the browser console:\n", "\n", "```javascript\n", "// Set up event listener for our custom events\n", "document.addEventListener('jupyter_widget_update', function(event) {\n", "    console.log('Custom event received:', event.detail);\n", "    // Here we would need to find a way to execute the code\n", "});\n", "\n", "// Test the event system\n", "updateLogtalkWidget('discovery_test', 'event_test_value');\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Expected Results\n", "\n", "The console tests will show us:\n", "1. What global objects are available\n", "2. What functions might be able to execute code\n", "3. Whether alternative communication methods work\n", "4. How your specific JupyterLab environment is configured\n", "\n", "Based on the results, we can create a targeted fix for your environment."]}], "metadata": {"kernelspec": {"display_name": "Logtalk", "language": "logtalk", "name": "logtalk"}, "language_info": {"codemirror_mode": "prolog", "file_extension": ".lgt", "mimetype": "text/x-logtalk", "name": "logtalk", "pygments_lexer": "prolog", "version": "3.0"}}, "nbformat": 4, "nbformat_minor": 4}