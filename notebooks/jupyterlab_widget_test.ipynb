{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ju<PERSON>terLab Widget Test\n", "\n", "This notebook tests HTML/JavaScript widgets specifically in JupyterLab environment."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Environment Detection\n", "\n", "First, let's check what's available in the JupyterLab environment:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "        <div class=\"logtalk-widget-container\">\n", "            <script>console.log(\"=== JupyterLab Environment Debug ===\"); console.log(\"window.jupyterapp:\", typeof window.jupyterapp); console.log(\"Jupyter:\", typeof Jupyter); console.log(\"IPython:\", typeof IPython); if (typeof window.jupyterapp !== \"undefined\") { console.log(\"JupyterLab app:\", window.jupyterapp); console.log(\"Current widget:\", window.jupyterapp.shell ? window.jupyterapp.shell.currentWidget : \"no shell\"); if (window.jupyterapp.shell && window.jupyterapp.shell.currentWidget) { const widget = window.jupyterapp.shell.currentWidget; console.log(\"Widget context:\", widget.context); if (widget.context && widget.context.sessionContext) { console.log(\"Session context:\", widget.context.sessionContext); console.log(\"Session:\", widget.context.sessionContext.session); if (widget.context.sessionContext.session) { console.log(\"Kernel:\", widget.context.sessionContext.session.kernel); } } } } console.log(\"=== End Debug ===\");</script>\n", "        </div>\n", "        <script>\n", "            // Ensure widget library is loaded only once\n", "            if (typeof LogtalkWidgets === 'undefined') {\n", "                /**\n", " * Logtalk Jupyter <PERSON> Widget Support\n", " * JavaScript communication layer for HTML/JavaScript widgets\n", " */\n", "\n", "// Global widget state management\n", "window.LogtalkWidgets = {\n", "    widgets: new Map(),\n", "    kernel: null,\n", "    \n", "    // Initialize widget system\n", "    init: function() {\n", "        // Get reference to Jupyter kernel with multiple fallback methods\n", "        this.kernel = null;\n", "\n", "        // Method 1: JupyterLab API\n", "        if (typeof window.jupyterapp !== 'undefined' && window.jupyterapp.shell) {\n", "            try {\n", "                const currentWidget = window.jupyterapp.shell.currentWidget;\n", "                console.log('Ju<PERSON>terLab currentWidget:', currentWidget);\n", "                if (currentWidget && currentWidget.context && currentWidget.context.sessionContext) {\n", "                    console.log('JupyterLab sessionContext:', currentWidget.context.sessionContext);\n", "                    const session = currentWidget.context.sessionContext.session;\n", "                    console.log('Ju<PERSON><PERSON>Lab session:', session);\n", "                    if (session && session.kernel) {\n", "                        this.kernel = session.kernel;\n", "                        console.log('Kernel found via JupyterLab API:', session.kernel);\n", "                    } else {\n", "                        console.log('JupyterLab session or kernel not available');\n", "                    }\n", "                } else {\n", "                    console.log('JupyterLab context or sessionContext not available');\n", "                }\n", "            } catch (e) {\n", "                console.log('JupyterLab API method failed:', e);\n", "            }\n", "        } else {\n", "            console.log('JupyterLab app not available');\n", "        }\n", "\n", "        // Method 2: Direct Jupyter reference (Classic Notebook)\n", "        if (!this.kernel && typeof Jupyter !== 'undefined' && Jupyter.notebook && Jupyter.notebook.kernel) {\n", "            this.kernel = Jupyter.notebook.kernel;\n", "            console.log('<PERSON><PERSON> found via Jupyter.notebook.kernel');\n", "        }\n", "\n", "        // Method 3: Try to find JupyterLab kernel through document\n", "        if (!this.kernel) {\n", "            try {\n", "                // Look for JupyterLab kernel in the document's scripts\n", "                const scripts = document.querySelectorAll('script');\n", "                for (let script of scripts) {\n", "                    if (script.textContent && script.textContent.includes('jupyter-widgets')) {\n", "                        // Try to access JupyterLab's kernel manager\n", "                        if (window.require) {\n", "                            window.require(['@jupyterlab/services'], function(services) {\n", "                                console.log('JupyterLab services available:', services);\n", "                            });\n", "                        }\n", "                        break;\n", "                    }\n", "                }\n", "            } catch (e) {\n", "                console.log('JupyterLab document search failed:', e);\n", "            }\n", "        }\n", "\n", "        // Method 4: Parent window <PERSON>\n", "        if (!this.kernel && typeof window.parent !== 'undefined' && window.parent.Jupyter &&\n", "                 window.parent.Jupyter.notebook && window.parent.Jupyter.notebook.kernel) {\n", "            this.kernel = window.parent.Jupyter.notebook.kernel;\n", "            console.log('Kernel found via parent window');\n", "        }\n", "\n", "        // Method 5: <PERSON>yt<PERSON> fallback\n", "        if (!this.kernel && typeof window.IPython !== 'undefined' && window.IPython.notebook &&\n", "                 window.IPython.notebook.kernel) {\n", "            this.kernel = window.IPython.notebook.kernel;\n", "            console.log('Kernel found via IPython.notebook.kernel');\n", "        }\n", "\n", "        // Method 6: Wait and retry\n", "        if (!this.kernel) {\n", "            console.log('<PERSON><PERSON> not immediately available, will retry...');\n", "            setTimeout(() => this.init(), 500);\n", "            return;\n", "        }\n", "\n", "        console.log('Logtalk Widgets initialized with kernel:', !!this.kernel);\n", "    },\n", "    \n", "    // Register a widget\n", "    registerWidget: function(widgetId, type, initialValue) {\n", "        this.widgets.set(widgetId, {\n", "            type: type,\n", "            value: initialValue,\n", "            element: document.getElementById(widgetId)\n", "        });\n", "    },\n", "    \n", "    // Update widget value and notify kernel\n", "    updateWidget: function(widgetId, value) {\n", "        if (this.widgets.has(widgetId)) {\n", "            const widget = this.widgets.get(widgetId);\n", "            widget.value = value;\n", "            \n", "            // Send update to kernel\n", "            this.sendWidgetUpdate(widgetId, value);\n", "        }\n", "    },\n", "    \n", "    // Send widget update to Logtalk kernel\n", "    sendWidgetUpdate: function(widgetId, value) {\n", "        // Properly escape the value for <PERSON><PERSON><PERSON>\n", "        let escapedValue;\n", "        if (typeof value === 'string') {\n", "            // Escape single quotes and backslashes for Logtalk string literals\n", "            escapedValue = value.replace(/\\\\/g, '\\\\\\\\').replace(/'/g, \"\\\\'\");\n", "            escapedValue = `'${escapedValue}'`;\n", "        } else if (typeof value === 'boolean') {\n", "            escapedValue = value ? 'true' : 'false';\n", "        } else {\n", "            escapedValue = String(value);\n", "        }\n", "\n", "        const code = `jupyter_widget_handling::set_widget_value('${widgetId}', ${escapedValue}).`;\n", "\n", "        console.log('Attempting to send widget update:', code);\n", "\n", "        // Try multiple execution methods in order of preference\n", "        const executionMethods = [\n", "            // Method 1: Use stored kernel reference\n", "            () => {\n", "                if (this.kernel) {\n", "                    this.kernel.execute(code, {\n", "                        silent: true,\n", "                        store_history: false,\n", "                        user_expressions: {},\n", "                        allow_stdin: false,\n", "                        stop_on_error: false\n", "                    });\n", "                    return true;\n", "                }\n", "                return false;\n", "            },\n", "\n", "            // Method 2: Direct Jupyter access\n", "            () => {\n", "                if (typeof Jupyter !== 'undefined' && Jupyter.notebook && Jupyter.notebook.kernel) {\n", "                    Jupyter.notebook.kernel.execute(code, {\n", "                        silent: true,\n", "                        store_history: false,\n", "                        user_expressions: {},\n", "                        allow_stdin: false,\n", "                        stop_on_error: false\n", "                    });\n", "                    this.kernel = Jupyter.notebook.kernel; // Store for future use\n", "                    return true;\n", "                }\n", "                return false;\n", "            },\n", "\n", "            // Method 3: Try parent window\n", "            () => {\n", "                if (typeof window.parent !== 'undefined' &&\n", "                    window.parent.Jupyter &&\n", "                    window.parent.Jupyter.notebook &&\n", "                    window.parent.Jupyter.notebook.kernel) {\n", "                    window.parent.Jupyter.notebook.kernel.execute(code, {\n", "                        silent: true,\n", "                        store_history: false,\n", "                        user_expressions: {},\n", "                        allow_stdin: false,\n", "                        stop_on_error: false\n", "                    });\n", "                    this.kernel = window.parent.Jupyter.notebook.kernel;\n", "                    return true;\n", "                }\n", "                return false;\n", "            },\n", "\n", "            // Method 4: <PERSON> fallback\n", "            () => {\n", "                if (typeof IPython !== 'undefined' && IPython.notebook && IPython.notebook.kernel) {\n", "                    IPython.notebook.kernel.execute(code, {\n", "                        silent: true,\n", "                        store_history: false,\n", "                        user_expressions: {},\n", "                        allow_stdin: false,\n", "                        stop_on_error: false\n", "                    });\n", "                    this.kernel = IPython.notebook.kernel;\n", "                    return true;\n", "                }\n", "                return false;\n", "            }\n", "        ];\n", "\n", "        // Try each method until one succeeds\n", "        for (let i = 0; i < executionMethods.length; i++) {\n", "            try {\n", "                if (executionMethods[i]()) {\n", "                    console.log(`Widget update sent successfully via method ${i + 1}`);\n", "                    return;\n", "                }\n", "            } catch (error) {\n", "                console.log(`Execution method ${i + 1} failed:`, error);\n", "            }\n", "        }\n", "\n", "        // If all methods fail, log error and store value locally\n", "        console.error('All kernel execution methods failed. Storing value locally.');\n", "        if (this.widgets.has(widgetId)) {\n", "            this.widgets.get(widgetId).value = value;\n", "            console.log(`Value stored locally for widget ${widgetId}:`, value);\n", "        }\n", "    },\n", "    \n", "    // Get widget value\n", "    getWidgetValue: function(widgetId) {\n", "        if (this.widgets.has(widgetId)) {\n", "            return this.widgets.get(widgetId).value;\n", "        }\n", "        return null;\n", "    },\n", "    \n", "    // Set widget value from kernel\n", "    setWidgetValue: function(widgetId, value) {\n", "        if (this.widgets.has(widgetId)) {\n", "            const widget = this.widgets.get(widgetId);\n", "            widget.value = value;\n", "            \n", "            // Update DOM element\n", "            const element = widget.element;\n", "            if (element) {\n", "                switch (widget.type) {\n", "                    case 'text_input':\n", "                    case 'number_input':\n", "                        element.value = value;\n", "                        break;\n", "                    case 'slider':\n", "                        element.value = value;\n", "                        // Update display value\n", "                        const valueDisplay = document.getElementById(widgetId + '_value');\n", "                        if (valueDisplay) {\n", "                            valueDisplay.textContent = value;\n", "                        }\n", "                        break;\n", "                    case 'dropdown':\n", "                        element.value = value;\n", "                        break;\n", "                    case 'checkbox':\n", "                        element.checked = (value === true || value === 'true');\n", "                        break;\n", "                }\n", "            }\n", "        }\n", "    },\n", "    \n", "    // Remove widget\n", "    removeWidget: function(widgetId) {\n", "        if (this.widgets.has(widgetId)) {\n", "            this.widgets.delete(widgetId);\n", "            \n", "            // Remove DOM element\n", "            const container = document.getElementById('container_' + widgetId);\n", "            if (container) {\n", "                container.remove();\n", "            }\n", "        }\n", "    },\n", "    \n", "    // Clear all widgets\n", "    clearAllWidgets: function() {\n", "        this.widgets.forEach((widget, widgetId) => {\n", "            this.removeWidget(widgetId);\n", "        });\n", "        this.widgets.clear();\n", "    },\n", "\n", "    // Debug function to check widget state\n", "    debugWidgets: function() {\n", "        console.log('Registered widgets:', this.widgets);\n", "        console.log('Kernel available:', !!this.kernel);\n", "        this.widgets.forEach((widget, widgetId) => {\n", "            console.log(`Widget ${widgetId}:`, {\n", "                type: widget.type,\n", "                value: widget.value,\n", "                element: widget.element,\n", "                elementValue: widget.element ? widget.element.value : 'N/A'\n", "            });\n", "        });\n", "    }\n", "};\n", "\n", "// Universal kernel execution function\n", "function executeInKernel(code, options = {}) {\n", "    console.log('Attempting to execute in kernel:', code);\n", "\n", "    const defaultOptions = {\n", "        silent: true,\n", "        store_history: false,\n", "        user_expressions: {},\n", "        allow_stdin: false,\n", "        stop_on_error: false,\n", "        ...options\n", "    };\n", "\n", "    // Try multiple kernel access methods\n", "    const kernelMethods = [\n", "        // Method 1: Standard Jupyter notebook\n", "        () => {\n", "            if (typeof Jupyter !== 'undefined' && Jupyter.notebook && Jupyter.notebook.kernel) {\n", "                Jupyter.notebook.kernel.execute(code, defaultOptions);\n", "                return 'Jupyter.notebook.kernel';\n", "            }\n", "            return null;\n", "        },\n", "\n", "        // Method 2: IPython fallback\n", "        () => {\n", "            if (typeof IPython !== 'undefined' && IPython.notebook && IPython.notebook.kernel) {\n", "                IPython.notebook.kernel.execute(code, defaultOptions);\n", "                return 'IPython.notebook.kernel';\n", "            }\n", "            return null;\n", "        },\n", "\n", "        // Method 3: Parent window access\n", "        () => {\n", "            if (window.parent && window.parent.Jupyter && window.parent.Jupyter.notebook && window.parent.Jupyter.notebook.kernel) {\n", "                window.parent.Jupyter.notebook.kernel.execute(code, defaultOptions);\n", "                return 'window.parent.Jupyter.notebook.kernel';\n", "            }\n", "            return null;\n", "        },\n", "\n", "        // Method 4: Top window access\n", "        () => {\n", "            if (window.top && window.top.Jupyter && window.top.Jupyter.notebook && window.top.Jupyter.notebook.kernel) {\n", "                window.top.Jupyter.notebook.kernel.execute(code, defaultOptions);\n", "                return 'window.top.Jupyter.notebook.kernel';\n", "            }\n", "            return null;\n", "        },\n", "\n", "        // Method 5: VS Code notebook API\n", "        () => {\n", "            if (typeof acquireVsCodeApi !== 'undefined') {\n", "                try {\n", "                    const vscode = acquireVsCodeApi();\n", "                    // VS Code has a different API - we'll need to post a message\n", "                    vscode.postMessage({\n", "                        type: 'executeCode',\n", "                        code: code\n", "                    });\n", "                    return 'VS Code API';\n", "                } catch (e) {\n", "                    console.log('VS Code API failed:', e);\n", "                }\n", "            }\n", "            return null;\n", "        },\n", "\n", "        // Method 6: <PERSON><PERSON><PERSON><PERSON>ab context\n", "        () => {\n", "            console.log('Trying JupyterLab execution method...');\n", "            // Try to find JupyterLab kernel through various paths\n", "            const labPaths = [\n", "                'window.jup<PERSON><PERSON><PERSON>',\n", "                'window.parent.jup<PERSON><PERSON><PERSON>',\n", "                'window.top.jup<PERSON><PERSON>p'\n", "            ];\n", "\n", "            for (const path of labPaths) {\n", "                try {\n", "                    console.log(`Trying JupyterLab path: ${path}`);\n", "                    const app = eval(path);\n", "                    console.log(`App found at ${path}:`, app);\n", "                    if (app && app.shell && app.shell.currentWidget) {\n", "                        const widget = app.shell.currentWidget;\n", "                        console.log(`Current widget:`, widget);\n", "                        if (widget.context && widget.context.sessionContext && widget.context.sessionContext.session) {\n", "                            const kernel = widget.context.sessionContext.session.kernel;\n", "                            console.log(`<PERSON><PERSON> found:`, kernel);\n", "                            if (kernel && kernel.requestExecute) {\n", "                                console.log(`Executing code via JupyterLab: ${code}`);\n", "                                const future = kernel.requestExecute({ code: code });\n", "                                console.log('JupyterLab execution future:', future);\n", "                                return `<PERSON><PERSON><PERSON><PERSON><PERSON> via ${path}`;\n", "                            } else {\n", "                                console.log('Kernel or requestExecute not available');\n", "                            }\n", "                        } else {\n", "                            console.log('Context, sessionContext, or session not available');\n", "                        }\n", "                    } else {\n", "                        console.log('App, shell, or currentWidget not available');\n", "                    }\n", "                } catch (e) {\n", "                    console.log(`JupyterLab method ${path} failed:`, e);\n", "                }\n", "            }\n", "            console.log('All JupyterLab paths failed');\n", "            return null;\n", "        },\n", "\n", "        // Method 7: JupyterLab direct kernel access\n", "        () => {\n", "            if (typeof window.jupyterapp !== 'undefined') {\n", "                try {\n", "                    const app = window.jupyterapp;\n", "                    const currentWidget = app.shell.currentWidget;\n", "                    if (currentWidget && currentWidget.context) {\n", "                        const sessionContext = currentWidget.context.sessionContext;\n", "                        if (sessionContext && sessionContext.session && sessionContext.session.kernel) {\n", "                            const kernel = sessionContext.session.kernel;\n", "                            const future = kernel.requestExecute({ code: code });\n", "                            console.log('<PERSON><PERSON><PERSON><PERSON><PERSON> direct execution:', future);\n", "                            return '<PERSON><PERSON><PERSON><PERSON><PERSON> direct';\n", "                        }\n", "                    }\n", "                } catch (e) {\n", "                    console.log('Jupyter<PERSON>ab direct method failed:', e);\n", "                }\n", "            }\n", "            return null;\n", "        }\n", "    ];\n", "\n", "    // Try each method until one succeeds\n", "    for (let i = 0; i < kernelMethods.length; i++) {\n", "        try {\n", "            const result = kernelMethods[i]();\n", "            if (result) {\n", "                console.log(`✅ Code executed successfully via: ${result}`);\n", "                return true;\n", "            }\n", "        } catch (error) {\n", "            console.log(`❌ Kernel method ${i + 1} failed:`, error);\n", "        }\n", "    }\n", "\n", "    console.error('❌ All kernel execution methods failed');\n", "    return false;\n", "}\n", "\n", "// Global function for widget updates (called from HTML)\n", "function updateLogtalkWidget(widgetId, value) {\n", "    console.log('updateLogtalkWidget called:', widgetId, value);\n", "\n", "    // Properly escape the value for <PERSON><PERSON><PERSON>\n", "    let escapedValue;\n", "    if (typeof value === 'string') {\n", "        escapedValue = `'${value.replace(/\\\\/g, '\\\\\\\\').replace(/'/g, \"\\\\'\")}'`;\n", "    } else if (typeof value === 'boolean') {\n", "        escapedValue = value ? 'true' : 'false';\n", "    } else {\n", "        escapedValue = String(value);\n", "    }\n", "\n", "    const code = `jupyter_widget_handling::set_widget_value('${widgetId}', ${escapedValue}).`;\n", "\n", "    // Try universal kernel execution\n", "    const success = executeInKernel(code);\n", "\n", "    if (success) {\n", "        console.log('✅ Widget update sent successfully');\n", "\n", "        // Also update local state if LogtalkWidgets is available\n", "        if (typeof LogtalkWidgets !== 'undefined' && LogtalkWidgets.widgets && LogtalkWidgets.widgets.has(widgetId)) {\n", "            LogtalkWidgets.widgets.get(widgetId).value = value;\n", "        }\n", "    } else {\n", "        console.error('❌ Widget update failed - storing locally only');\n", "\n", "        // Store locally as fallback\n", "        if (typeof LogtalkWidgets !== 'undefined') {\n", "            if (!LogtalkWidgets.widgets) {\n", "                LogtalkWidgets.widgets = new Map();\n", "            }\n", "            if (!LogtalkWidgets.widgets.has(widgetId)) {\n", "                LogtalkWidgets.widgets.set(widgetId, { type: 'unknown', value: value });\n", "            } else {\n", "                LogtalkWidgets.widgets.get(widgetId).value = value;\n", "            }\n", "            console.log('Value stored locally for widget:', widgetId, value);\n", "        }\n", "    }\n", "}\n", "\n", "// Auto-register widgets when they are created\n", "function autoRegisterWidget(widgetId, type, initialValue) {\n", "    LogtalkWidgets.registerWidget(widgetId, type, initialValue);\n", "}\n", "\n", "// Manual kernel connection function for debugging\n", "function connectLogtalkWidgetsToKernel() {\n", "    console.log('Attempting manual kernel connection...');\n", "\n", "    // Try all possible kernel references\n", "    const kernelSources = [\n", "        () => Jupyter && Jupyter.notebook && Jupyter.notebook.kernel,\n", "        () => IPython && IPython.notebook && IPython.notebook.kernel,\n", "        () => window.parent.Jupyter && window.parent.Jupyter.notebook && window.parent.Jupyter.notebook.kernel,\n", "        () => window.parent.IPython && window.parent.IPython.notebook && window.parent.IPython.notebook.kernel\n", "    ];\n", "\n", "    for (let i = 0; i < kernelSources.length; i++) {\n", "        try {\n", "            const kernel = kernelSources[i]();\n", "            if (kernel) {\n", "                LogtalkWidgets.kernel = kernel;\n", "                console.log(`Kernel connected via method ${i + 1}:`, kernel);\n", "                return true;\n", "            }\n", "        } catch (e) {\n", "            console.log(`Kernel connection method ${i + 1} failed:`, e);\n", "        }\n", "    }\n", "\n", "    console.log('All kernel connection methods failed');\n", "    return false;\n", "}\n", "\n", "// Make functions globally available for debugging\n", "window.connectLogtalkWidgetsToKernel = connectLogtalkWidgetsToKernel;\n", "window.LogtalkWidgets = LogtalkWidgets;\n", "\n", "// Multiple initialization strategies\n", "function initializeWidgets() {\n", "    LogtalkWidgets.init();\n", "\n", "    // Keep trying until kernel is available\n", "    if (!LogtalkWidgets.kernel) {\n", "        setTimeout(initializeWidgets, 1000);\n", "    }\n", "}\n", "\n", "// Initialize when DOM is ready\n", "if (document.readyState === 'loading') {\n", "    document.addEventListener('DOMContentLoaded', initializeWidgets);\n", "} else {\n", "    initializeWidgets();\n", "}\n", "\n", "// Initialize when <PERSON><PERSON><PERSON> is ready (for notebook environment)\n", "if (typeof Jupyter !== 'undefined') {\n", "    // Try multiple Jupyter events\n", "    if (Jupyter.notebook && Jupyter.notebook.events) {\n", "        Jupyter.notebook.events.on('kernel_ready.Kernel', function() {\n", "            console.log('Kernel ready event fired');\n", "            LogtalkWidgets.init();\n", "        });\n", "\n", "        Jupyter.notebook.events.on('kernel_connected.Kernel', function() {\n", "            console.log('Kernel connected event fired');\n", "            LogtalkWidgets.init();\n", "        });\n", "    }\n", "}\n", "\n", "// Fallback: Keep trying to initialize\n", "setTimeout(function() {\n", "    if (!LogtalkWidgets.kernel) {\n", "        console.log('Fallback initialization attempt');\n", "        LogtalkWidgets.init();\n", "    }\n", "}, 2000);\n", "\n", "// CSS styles for widgets\n", "const widgetStyles = `\n", "<style>\n", ".logtalk-widget {\n", "    margin: 10px 0;\n", "    padding: 10px;\n", "    border: 1px solid #e0e0e0;\n", "    border-radius: 5px;\n", "    background-color: #fafafa;\n", "    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n", "}\n", "\n", ".logtalk-widget label {\n", "    font-weight: 500;\n", "    color: #333;\n", "    margin-bottom: 5px;\n", "    display: inline-block;\n", "}\n", "\n", ".logtalk-widget input[type=\"text\"],\n", ".logtalk-widget input[type=\"number\"],\n", ".logtalk-widget select {\n", "    width: 200px;\n", "    padding: 6px 10px;\n", "    border: 1px solid #ccc;\n", "    border-radius: 4px;\n", "    font-size: 14px;\n", "    transition: border-color 0.3s ease;\n", "}\n", "\n", ".logtalk-widget input[type=\"text\"]:focus,\n", ".logtalk-widget input[type=\"number\"]:focus,\n", ".logtalk-widget select:focus {\n", "    outline: none;\n", "    border-color: #007cba;\n", "    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2);\n", "}\n", "\n", ".logtalk-widget input[type=\"range\"] {\n", "    width: 200px;\n", "    margin: 5px 0;\n", "}\n", "\n", ".logtalk-widget input[type=\"checkbox\"] {\n", "    margin-right: 8px;\n", "    transform: scale(1.2);\n", "}\n", "\n", ".logtalk-widget button {\n", "    background-color: #007cba;\n", "    color: white;\n", "    border: none;\n", "    padding: 8px 16px;\n", "    border-radius: 4px;\n", "    cursor: pointer;\n", "    font-size: 14px;\n", "    transition: background-color 0.3s ease;\n", "}\n", "\n", ".logtalk-widget button:hover {\n", "    background-color: #005a87;\n", "}\n", "\n", ".logtalk-widget button:active {\n", "    background-color: #004a73;\n", "}\n", "</style>\n", "`;\n", "\n", "// Inject CSS styles\n", "if (document.head) {\n", "    document.head.insertAdjacentHTML('beforeend', widgetStyles);\n", "}\n", "\n", "                console.log('Logtalk widget library loaded');\n", "            } else {\n", "                console.log('Logtalk widget library already available');\n", "            }\n", "\n", "            // Ensure kernel reference is updated\n", "            if (typeof LogtalkWidgets !== 'undefined') {\n", "                LogtalkWidgets.init();\n", "            }\n", "        </script>\n", "        "], "text/plain": ["Logtalk Widget"]}, "metadata": {"text/html": {"isolated": false}}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["% This will include JavaScript to detect the environment\n", "jupyter_term_handling::assert_success_response(debug, [], '', [widget_html-'<script>console.log(\"=== JupyterLab Environment Debug ===\"); console.log(\"window.jupyterapp:\", typeof window.jupyterapp); console.log(\"Jupyter:\", typeof Jupyter); console.log(\"IPython:\", typeof IPython); if (typeof window.jupyterapp !== \"undefined\") { console.log(\"JupyterLab app:\", window.jupyterapp); console.log(\"Current widget:\", window.jupyterapp.shell ? window.jupyterapp.shell.currentWidget : \"no shell\"); if (window.jupyterapp.shell && window.jupyterapp.shell.currentWidget) { const widget = window.jupyterapp.shell.currentWidget; console.log(\"Widget context:\", widget.context); if (widget.context && widget.context.sessionContext) { console.log(\"Session context:\", widget.context.sessionContext); console.log(\"Session:\", widget.context.sessionContext.session); if (widget.context.sessionContext.session) { console.log(\"Kernel:\", widget.context.sessionContext.session.kernel); } } } } console.log(\"=== End Debug ===\");</script>'])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Test Widget Creation\n", "\n", "Create a simple text widget:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "        <div class=\"logtalk-widget-container\">\n", "            <div class=\"logtalk-widget\" id=\"container_lab_test_widget\"><label for=\"lab_test_widget\">Test in JupyterLab:</label><br><input type=\"text\" id=\"lab_test_widget\" value=\"Hello JupyterLab\" onchange=\"updateLogtalkWidget('lab_test_widget', this.value)\" style=\"margin: 5px; padding: 5px; border: 1px solid #ccc; border-radius: 3px;\"></div><script>setTimeout(function() {  if (typeof autoRegisterWidget === \"function\") {    autoRegisterWidget(\"lab_test_widget\", \"text_input\", \"Hello JupyterLab\");  }}, 100);</script>\n", "        </div>\n", "        <script>\n", "            // Ensure widget library is loaded only once\n", "            if (typeof LogtalkWidgets === 'undefined') {\n", "                /**\n", " * Logtalk Jupyter <PERSON> Widget Support\n", " * JavaScript communication layer for HTML/JavaScript widgets\n", " */\n", "\n", "// Global widget state management\n", "window.LogtalkWidgets = {\n", "    widgets: new Map(),\n", "    kernel: null,\n", "    \n", "    // Initialize widget system\n", "    init: function() {\n", "        // Get reference to Jupyter kernel with multiple fallback methods\n", "        this.kernel = null;\n", "\n", "        // Method 1: JupyterLab API\n", "        if (typeof window.jupyterapp !== 'undefined' && window.jupyterapp.shell) {\n", "            try {\n", "                const currentWidget = window.jupyterapp.shell.currentWidget;\n", "                console.log('Ju<PERSON>terLab currentWidget:', currentWidget);\n", "                if (currentWidget && currentWidget.context && currentWidget.context.sessionContext) {\n", "                    console.log('JupyterLab sessionContext:', currentWidget.context.sessionContext);\n", "                    const session = currentWidget.context.sessionContext.session;\n", "                    console.log('Ju<PERSON><PERSON>Lab session:', session);\n", "                    if (session && session.kernel) {\n", "                        this.kernel = session.kernel;\n", "                        console.log('Kernel found via JupyterLab API:', session.kernel);\n", "                    } else {\n", "                        console.log('JupyterLab session or kernel not available');\n", "                    }\n", "                } else {\n", "                    console.log('JupyterLab context or sessionContext not available');\n", "                }\n", "            } catch (e) {\n", "                console.log('JupyterLab API method failed:', e);\n", "            }\n", "        } else {\n", "            console.log('JupyterLab app not available');\n", "        }\n", "\n", "        // Method 2: Direct Jupyter reference (Classic Notebook)\n", "        if (!this.kernel && typeof Jupyter !== 'undefined' && Jupyter.notebook && Jupyter.notebook.kernel) {\n", "            this.kernel = Jupyter.notebook.kernel;\n", "            console.log('<PERSON><PERSON> found via Jupyter.notebook.kernel');\n", "        }\n", "\n", "        // Method 3: Try to find JupyterLab kernel through document\n", "        if (!this.kernel) {\n", "            try {\n", "                // Look for JupyterLab kernel in the document's scripts\n", "                const scripts = document.querySelectorAll('script');\n", "                for (let script of scripts) {\n", "                    if (script.textContent && script.textContent.includes('jupyter-widgets')) {\n", "                        // Try to access JupyterLab's kernel manager\n", "                        if (window.require) {\n", "                            window.require(['@jupyterlab/services'], function(services) {\n", "                                console.log('JupyterLab services available:', services);\n", "                            });\n", "                        }\n", "                        break;\n", "                    }\n", "                }\n", "            } catch (e) {\n", "                console.log('JupyterLab document search failed:', e);\n", "            }\n", "        }\n", "\n", "        // Method 4: Parent window <PERSON>\n", "        if (!this.kernel && typeof window.parent !== 'undefined' && window.parent.Jupyter &&\n", "                 window.parent.Jupyter.notebook && window.parent.Jupyter.notebook.kernel) {\n", "            this.kernel = window.parent.Jupyter.notebook.kernel;\n", "            console.log('Kernel found via parent window');\n", "        }\n", "\n", "        // Method 5: <PERSON>yt<PERSON> fallback\n", "        if (!this.kernel && typeof window.IPython !== 'undefined' && window.IPython.notebook &&\n", "                 window.IPython.notebook.kernel) {\n", "            this.kernel = window.IPython.notebook.kernel;\n", "            console.log('Kernel found via IPython.notebook.kernel');\n", "        }\n", "\n", "        // Method 6: Wait and retry\n", "        if (!this.kernel) {\n", "            console.log('<PERSON><PERSON> not immediately available, will retry...');\n", "            setTimeout(() => this.init(), 500);\n", "            return;\n", "        }\n", "\n", "        console.log('Logtalk Widgets initialized with kernel:', !!this.kernel);\n", "    },\n", "    \n", "    // Register a widget\n", "    registerWidget: function(widgetId, type, initialValue) {\n", "        this.widgets.set(widgetId, {\n", "            type: type,\n", "            value: initialValue,\n", "            element: document.getElementById(widgetId)\n", "        });\n", "    },\n", "    \n", "    // Update widget value and notify kernel\n", "    updateWidget: function(widgetId, value) {\n", "        if (this.widgets.has(widgetId)) {\n", "            const widget = this.widgets.get(widgetId);\n", "            widget.value = value;\n", "            \n", "            // Send update to kernel\n", "            this.sendWidgetUpdate(widgetId, value);\n", "        }\n", "    },\n", "    \n", "    // Send widget update to Logtalk kernel\n", "    sendWidgetUpdate: function(widgetId, value) {\n", "        // Properly escape the value for <PERSON><PERSON><PERSON>\n", "        let escapedValue;\n", "        if (typeof value === 'string') {\n", "            // Escape single quotes and backslashes for Logtalk string literals\n", "            escapedValue = value.replace(/\\\\/g, '\\\\\\\\').replace(/'/g, \"\\\\'\");\n", "            escapedValue = `'${escapedValue}'`;\n", "        } else if (typeof value === 'boolean') {\n", "            escapedValue = value ? 'true' : 'false';\n", "        } else {\n", "            escapedValue = String(value);\n", "        }\n", "\n", "        const code = `jupyter_widget_handling::set_widget_value('${widgetId}', ${escapedValue}).`;\n", "\n", "        console.log('Attempting to send widget update:', code);\n", "\n", "        // Try multiple execution methods in order of preference\n", "        const executionMethods = [\n", "            // Method 1: Use stored kernel reference\n", "            () => {\n", "                if (this.kernel) {\n", "                    this.kernel.execute(code, {\n", "                        silent: true,\n", "                        store_history: false,\n", "                        user_expressions: {},\n", "                        allow_stdin: false,\n", "                        stop_on_error: false\n", "                    });\n", "                    return true;\n", "                }\n", "                return false;\n", "            },\n", "\n", "            // Method 2: Direct Jupyter access\n", "            () => {\n", "                if (typeof Jupyter !== 'undefined' && Jupyter.notebook && Jupyter.notebook.kernel) {\n", "                    Jupyter.notebook.kernel.execute(code, {\n", "                        silent: true,\n", "                        store_history: false,\n", "                        user_expressions: {},\n", "                        allow_stdin: false,\n", "                        stop_on_error: false\n", "                    });\n", "                    this.kernel = Jupyter.notebook.kernel; // Store for future use\n", "                    return true;\n", "                }\n", "                return false;\n", "            },\n", "\n", "            // Method 3: Try parent window\n", "            () => {\n", "                if (typeof window.parent !== 'undefined' &&\n", "                    window.parent.Jupyter &&\n", "                    window.parent.Jupyter.notebook &&\n", "                    window.parent.Jupyter.notebook.kernel) {\n", "                    window.parent.Jupyter.notebook.kernel.execute(code, {\n", "                        silent: true,\n", "                        store_history: false,\n", "                        user_expressions: {},\n", "                        allow_stdin: false,\n", "                        stop_on_error: false\n", "                    });\n", "                    this.kernel = window.parent.Jupyter.notebook.kernel;\n", "                    return true;\n", "                }\n", "                return false;\n", "            },\n", "\n", "            // Method 4: <PERSON> fallback\n", "            () => {\n", "                if (typeof IPython !== 'undefined' && IPython.notebook && IPython.notebook.kernel) {\n", "                    IPython.notebook.kernel.execute(code, {\n", "                        silent: true,\n", "                        store_history: false,\n", "                        user_expressions: {},\n", "                        allow_stdin: false,\n", "                        stop_on_error: false\n", "                    });\n", "                    this.kernel = IPython.notebook.kernel;\n", "                    return true;\n", "                }\n", "                return false;\n", "            }\n", "        ];\n", "\n", "        // Try each method until one succeeds\n", "        for (let i = 0; i < executionMethods.length; i++) {\n", "            try {\n", "                if (executionMethods[i]()) {\n", "                    console.log(`Widget update sent successfully via method ${i + 1}`);\n", "                    return;\n", "                }\n", "            } catch (error) {\n", "                console.log(`Execution method ${i + 1} failed:`, error);\n", "            }\n", "        }\n", "\n", "        // If all methods fail, log error and store value locally\n", "        console.error('All kernel execution methods failed. Storing value locally.');\n", "        if (this.widgets.has(widgetId)) {\n", "            this.widgets.get(widgetId).value = value;\n", "            console.log(`Value stored locally for widget ${widgetId}:`, value);\n", "        }\n", "    },\n", "    \n", "    // Get widget value\n", "    getWidgetValue: function(widgetId) {\n", "        if (this.widgets.has(widgetId)) {\n", "            return this.widgets.get(widgetId).value;\n", "        }\n", "        return null;\n", "    },\n", "    \n", "    // Set widget value from kernel\n", "    setWidgetValue: function(widgetId, value) {\n", "        if (this.widgets.has(widgetId)) {\n", "            const widget = this.widgets.get(widgetId);\n", "            widget.value = value;\n", "            \n", "            // Update DOM element\n", "            const element = widget.element;\n", "            if (element) {\n", "                switch (widget.type) {\n", "                    case 'text_input':\n", "                    case 'number_input':\n", "                        element.value = value;\n", "                        break;\n", "                    case 'slider':\n", "                        element.value = value;\n", "                        // Update display value\n", "                        const valueDisplay = document.getElementById(widgetId + '_value');\n", "                        if (valueDisplay) {\n", "                            valueDisplay.textContent = value;\n", "                        }\n", "                        break;\n", "                    case 'dropdown':\n", "                        element.value = value;\n", "                        break;\n", "                    case 'checkbox':\n", "                        element.checked = (value === true || value === 'true');\n", "                        break;\n", "                }\n", "            }\n", "        }\n", "    },\n", "    \n", "    // Remove widget\n", "    removeWidget: function(widgetId) {\n", "        if (this.widgets.has(widgetId)) {\n", "            this.widgets.delete(widgetId);\n", "            \n", "            // Remove DOM element\n", "            const container = document.getElementById('container_' + widgetId);\n", "            if (container) {\n", "                container.remove();\n", "            }\n", "        }\n", "    },\n", "    \n", "    // Clear all widgets\n", "    clearAllWidgets: function() {\n", "        this.widgets.forEach((widget, widgetId) => {\n", "            this.removeWidget(widgetId);\n", "        });\n", "        this.widgets.clear();\n", "    },\n", "\n", "    // Debug function to check widget state\n", "    debugWidgets: function() {\n", "        console.log('Registered widgets:', this.widgets);\n", "        console.log('Kernel available:', !!this.kernel);\n", "        this.widgets.forEach((widget, widgetId) => {\n", "            console.log(`Widget ${widgetId}:`, {\n", "                type: widget.type,\n", "                value: widget.value,\n", "                element: widget.element,\n", "                elementValue: widget.element ? widget.element.value : 'N/A'\n", "            });\n", "        });\n", "    }\n", "};\n", "\n", "// Universal kernel execution function\n", "function executeInKernel(code, options = {}) {\n", "    console.log('Attempting to execute in kernel:', code);\n", "\n", "    const defaultOptions = {\n", "        silent: true,\n", "        store_history: false,\n", "        user_expressions: {},\n", "        allow_stdin: false,\n", "        stop_on_error: false,\n", "        ...options\n", "    };\n", "\n", "    // Try multiple kernel access methods\n", "    const kernelMethods = [\n", "        // Method 1: Standard Jupyter notebook\n", "        () => {\n", "            if (typeof Jupyter !== 'undefined' && Jupyter.notebook && Jupyter.notebook.kernel) {\n", "                Jupyter.notebook.kernel.execute(code, defaultOptions);\n", "                return 'Jupyter.notebook.kernel';\n", "            }\n", "            return null;\n", "        },\n", "\n", "        // Method 2: IPython fallback\n", "        () => {\n", "            if (typeof IPython !== 'undefined' && IPython.notebook && IPython.notebook.kernel) {\n", "                IPython.notebook.kernel.execute(code, defaultOptions);\n", "                return 'IPython.notebook.kernel';\n", "            }\n", "            return null;\n", "        },\n", "\n", "        // Method 3: Parent window access\n", "        () => {\n", "            if (window.parent && window.parent.Jupyter && window.parent.Jupyter.notebook && window.parent.Jupyter.notebook.kernel) {\n", "                window.parent.Jupyter.notebook.kernel.execute(code, defaultOptions);\n", "                return 'window.parent.Jupyter.notebook.kernel';\n", "            }\n", "            return null;\n", "        },\n", "\n", "        // Method 4: Top window access\n", "        () => {\n", "            if (window.top && window.top.Jupyter && window.top.Jupyter.notebook && window.top.Jupyter.notebook.kernel) {\n", "                window.top.Jupyter.notebook.kernel.execute(code, defaultOptions);\n", "                return 'window.top.Jupyter.notebook.kernel';\n", "            }\n", "            return null;\n", "        },\n", "\n", "        // Method 5: VS Code notebook API\n", "        () => {\n", "            if (typeof acquireVsCodeApi !== 'undefined') {\n", "                try {\n", "                    const vscode = acquireVsCodeApi();\n", "                    // VS Code has a different API - we'll need to post a message\n", "                    vscode.postMessage({\n", "                        type: 'executeCode',\n", "                        code: code\n", "                    });\n", "                    return 'VS Code API';\n", "                } catch (e) {\n", "                    console.log('VS Code API failed:', e);\n", "                }\n", "            }\n", "            return null;\n", "        },\n", "\n", "        // Method 6: <PERSON><PERSON><PERSON><PERSON>ab context\n", "        () => {\n", "            console.log('Trying JupyterLab execution method...');\n", "            // Try to find JupyterLab kernel through various paths\n", "            const labPaths = [\n", "                'window.jup<PERSON><PERSON><PERSON>',\n", "                'window.parent.jup<PERSON><PERSON><PERSON>',\n", "                'window.top.jup<PERSON><PERSON>p'\n", "            ];\n", "\n", "            for (const path of labPaths) {\n", "                try {\n", "                    console.log(`Trying JupyterLab path: ${path}`);\n", "                    const app = eval(path);\n", "                    console.log(`App found at ${path}:`, app);\n", "                    if (app && app.shell && app.shell.currentWidget) {\n", "                        const widget = app.shell.currentWidget;\n", "                        console.log(`Current widget:`, widget);\n", "                        if (widget.context && widget.context.sessionContext && widget.context.sessionContext.session) {\n", "                            const kernel = widget.context.sessionContext.session.kernel;\n", "                            console.log(`<PERSON><PERSON> found:`, kernel);\n", "                            if (kernel && kernel.requestExecute) {\n", "                                console.log(`Executing code via JupyterLab: ${code}`);\n", "                                const future = kernel.requestExecute({ code: code });\n", "                                console.log('JupyterLab execution future:', future);\n", "                                return `<PERSON><PERSON><PERSON><PERSON><PERSON> via ${path}`;\n", "                            } else {\n", "                                console.log('Kernel or requestExecute not available');\n", "                            }\n", "                        } else {\n", "                            console.log('Context, sessionContext, or session not available');\n", "                        }\n", "                    } else {\n", "                        console.log('App, shell, or currentWidget not available');\n", "                    }\n", "                } catch (e) {\n", "                    console.log(`JupyterLab method ${path} failed:`, e);\n", "                }\n", "            }\n", "            console.log('All JupyterLab paths failed');\n", "            return null;\n", "        },\n", "\n", "        // Method 7: JupyterLab direct kernel access\n", "        () => {\n", "            if (typeof window.jupyterapp !== 'undefined') {\n", "                try {\n", "                    const app = window.jupyterapp;\n", "                    const currentWidget = app.shell.currentWidget;\n", "                    if (currentWidget && currentWidget.context) {\n", "                        const sessionContext = currentWidget.context.sessionContext;\n", "                        if (sessionContext && sessionContext.session && sessionContext.session.kernel) {\n", "                            const kernel = sessionContext.session.kernel;\n", "                            const future = kernel.requestExecute({ code: code });\n", "                            console.log('<PERSON><PERSON><PERSON><PERSON><PERSON> direct execution:', future);\n", "                            return '<PERSON><PERSON><PERSON><PERSON><PERSON> direct';\n", "                        }\n", "                    }\n", "                } catch (e) {\n", "                    console.log('Jupyter<PERSON>ab direct method failed:', e);\n", "                }\n", "            }\n", "            return null;\n", "        }\n", "    ];\n", "\n", "    // Try each method until one succeeds\n", "    for (let i = 0; i < kernelMethods.length; i++) {\n", "        try {\n", "            const result = kernelMethods[i]();\n", "            if (result) {\n", "                console.log(`✅ Code executed successfully via: ${result}`);\n", "                return true;\n", "            }\n", "        } catch (error) {\n", "            console.log(`❌ Kernel method ${i + 1} failed:`, error);\n", "        }\n", "    }\n", "\n", "    console.error('❌ All kernel execution methods failed');\n", "    return false;\n", "}\n", "\n", "// Global function for widget updates (called from HTML)\n", "function updateLogtalkWidget(widgetId, value) {\n", "    console.log('updateLogtalkWidget called:', widgetId, value);\n", "\n", "    // Properly escape the value for <PERSON><PERSON><PERSON>\n", "    let escapedValue;\n", "    if (typeof value === 'string') {\n", "        escapedValue = `'${value.replace(/\\\\/g, '\\\\\\\\').replace(/'/g, \"\\\\'\")}'`;\n", "    } else if (typeof value === 'boolean') {\n", "        escapedValue = value ? 'true' : 'false';\n", "    } else {\n", "        escapedValue = String(value);\n", "    }\n", "\n", "    const code = `jupyter_widget_handling::set_widget_value('${widgetId}', ${escapedValue}).`;\n", "\n", "    // Try universal kernel execution\n", "    const success = executeInKernel(code);\n", "\n", "    if (success) {\n", "        console.log('✅ Widget update sent successfully');\n", "\n", "        // Also update local state if LogtalkWidgets is available\n", "        if (typeof LogtalkWidgets !== 'undefined' && LogtalkWidgets.widgets && LogtalkWidgets.widgets.has(widgetId)) {\n", "            LogtalkWidgets.widgets.get(widgetId).value = value;\n", "        }\n", "    } else {\n", "        console.error('❌ Widget update failed - storing locally only');\n", "\n", "        // Store locally as fallback\n", "        if (typeof LogtalkWidgets !== 'undefined') {\n", "            if (!LogtalkWidgets.widgets) {\n", "                LogtalkWidgets.widgets = new Map();\n", "            }\n", "            if (!LogtalkWidgets.widgets.has(widgetId)) {\n", "                LogtalkWidgets.widgets.set(widgetId, { type: 'unknown', value: value });\n", "            } else {\n", "                LogtalkWidgets.widgets.get(widgetId).value = value;\n", "            }\n", "            console.log('Value stored locally for widget:', widgetId, value);\n", "        }\n", "    }\n", "}\n", "\n", "// Auto-register widgets when they are created\n", "function autoRegisterWidget(widgetId, type, initialValue) {\n", "    LogtalkWidgets.registerWidget(widgetId, type, initialValue);\n", "}\n", "\n", "// Manual kernel connection function for debugging\n", "function connectLogtalkWidgetsToKernel() {\n", "    console.log('Attempting manual kernel connection...');\n", "\n", "    // Try all possible kernel references\n", "    const kernelSources = [\n", "        () => Jupyter && Jupyter.notebook && Jupyter.notebook.kernel,\n", "        () => IPython && IPython.notebook && IPython.notebook.kernel,\n", "        () => window.parent.Jupyter && window.parent.Jupyter.notebook && window.parent.Jupyter.notebook.kernel,\n", "        () => window.parent.IPython && window.parent.IPython.notebook && window.parent.IPython.notebook.kernel\n", "    ];\n", "\n", "    for (let i = 0; i < kernelSources.length; i++) {\n", "        try {\n", "            const kernel = kernelSources[i]();\n", "            if (kernel) {\n", "                LogtalkWidgets.kernel = kernel;\n", "                console.log(`Kernel connected via method ${i + 1}:`, kernel);\n", "                return true;\n", "            }\n", "        } catch (e) {\n", "            console.log(`Kernel connection method ${i + 1} failed:`, e);\n", "        }\n", "    }\n", "\n", "    console.log('All kernel connection methods failed');\n", "    return false;\n", "}\n", "\n", "// Make functions globally available for debugging\n", "window.connectLogtalkWidgetsToKernel = connectLogtalkWidgetsToKernel;\n", "window.LogtalkWidgets = LogtalkWidgets;\n", "\n", "// Multiple initialization strategies\n", "function initializeWidgets() {\n", "    LogtalkWidgets.init();\n", "\n", "    // Keep trying until kernel is available\n", "    if (!LogtalkWidgets.kernel) {\n", "        setTimeout(initializeWidgets, 1000);\n", "    }\n", "}\n", "\n", "// Initialize when DOM is ready\n", "if (document.readyState === 'loading') {\n", "    document.addEventListener('DOMContentLoaded', initializeWidgets);\n", "} else {\n", "    initializeWidgets();\n", "}\n", "\n", "// Initialize when <PERSON><PERSON><PERSON> is ready (for notebook environment)\n", "if (typeof Jupyter !== 'undefined') {\n", "    // Try multiple Jupyter events\n", "    if (Jupyter.notebook && Jupyter.notebook.events) {\n", "        Jupyter.notebook.events.on('kernel_ready.Kernel', function() {\n", "            console.log('Kernel ready event fired');\n", "            LogtalkWidgets.init();\n", "        });\n", "\n", "        Jupyter.notebook.events.on('kernel_connected.Kernel', function() {\n", "            console.log('Kernel connected event fired');\n", "            LogtalkWidgets.init();\n", "        });\n", "    }\n", "}\n", "\n", "// Fallback: Keep trying to initialize\n", "setTimeout(function() {\n", "    if (!LogtalkWidgets.kernel) {\n", "        console.log('Fallback initialization attempt');\n", "        LogtalkWidgets.init();\n", "    }\n", "}, 2000);\n", "\n", "// CSS styles for widgets\n", "const widgetStyles = `\n", "<style>\n", ".logtalk-widget {\n", "    margin: 10px 0;\n", "    padding: 10px;\n", "    border: 1px solid #e0e0e0;\n", "    border-radius: 5px;\n", "    background-color: #fafafa;\n", "    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n", "}\n", "\n", ".logtalk-widget label {\n", "    font-weight: 500;\n", "    color: #333;\n", "    margin-bottom: 5px;\n", "    display: inline-block;\n", "}\n", "\n", ".logtalk-widget input[type=\"text\"],\n", ".logtalk-widget input[type=\"number\"],\n", ".logtalk-widget select {\n", "    width: 200px;\n", "    padding: 6px 10px;\n", "    border: 1px solid #ccc;\n", "    border-radius: 4px;\n", "    font-size: 14px;\n", "    transition: border-color 0.3s ease;\n", "}\n", "\n", ".logtalk-widget input[type=\"text\"]:focus,\n", ".logtalk-widget input[type=\"number\"]:focus,\n", ".logtalk-widget select:focus {\n", "    outline: none;\n", "    border-color: #007cba;\n", "    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2);\n", "}\n", "\n", ".logtalk-widget input[type=\"range\"] {\n", "    width: 200px;\n", "    margin: 5px 0;\n", "}\n", "\n", ".logtalk-widget input[type=\"checkbox\"] {\n", "    margin-right: 8px;\n", "    transform: scale(1.2);\n", "}\n", "\n", ".logtalk-widget button {\n", "    background-color: #007cba;\n", "    color: white;\n", "    border: none;\n", "    padding: 8px 16px;\n", "    border-radius: 4px;\n", "    cursor: pointer;\n", "    font-size: 14px;\n", "    transition: background-color 0.3s ease;\n", "}\n", "\n", ".logtalk-widget button:hover {\n", "    background-color: #005a87;\n", "}\n", "\n", ".logtalk-widget button:active {\n", "    background-color: #004a73;\n", "}\n", "</style>\n", "`;\n", "\n", "// Inject CSS styles\n", "if (document.head) {\n", "    document.head.insertAdjacentHTML('beforeend', widgetStyles);\n", "}\n", "\n", "                console.log('Logtalk widget library loaded');\n", "            } else {\n", "                console.log('Logtalk widget library already available');\n", "            }\n", "\n", "            // Ensure kernel reference is updated\n", "            if (typeof LogtalkWidgets !== 'undefined') {\n", "                LogtalkWidgets.init();\n", "            }\n", "        </script>\n", "        "], "text/plain": ["Logtalk Widget"]}, "metadata": {"text/html": {"isolated": false}}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["jupyter::create_text_input(lab_test_widget, 'Test in JupyterLab:', 'Hello JupyterLab')."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Manual <PERSON>\n", "\n", "Test kernel execution manually:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "        <div class=\"logtalk-widget-container\">\n", "            <script>console.log(\"Testing manual kernel execution...\"); function testJupyterLabKernel() { if (typeof window.jupyterapp !== \"undefined\" && window.jupyterapp.shell) { try { const currentWidget = window.jupyterapp.shell.currentWidget; if (currentWidget && currentWidget.context && currentWidget.context.sessionContext) { const session = currentWidget.context.sessionContext.session; if (session && session.kernel) { console.log(\"Found JupyterLab kernel:\", session.kernel); const testCode = \"write('Manual kernel test successful!'), nl.\"; console.log(\"Executing test code:\", testCode); const future = session.kernel.requestExecute({ code: testCode }); console.log(\"Execution future:\", future); return true; } } } catch (e) { console.error(\"JupyterLab kernel test failed:\", e); } } console.log(\"JupyterLab kernel not available\"); return false; } const result = testJupyterLabKernel(); console.log(\"Manual kernel test result:\", result);</script>\n", "        </div>\n", "        <script>\n", "            // Ensure widget library is loaded only once\n", "            if (typeof LogtalkWidgets === 'undefined') {\n", "                /**\n", " * Logtalk Jupyter <PERSON> Widget Support\n", " * JavaScript communication layer for HTML/JavaScript widgets\n", " */\n", "\n", "// Global widget state management\n", "window.LogtalkWidgets = {\n", "    widgets: new Map(),\n", "    kernel: null,\n", "    \n", "    // Initialize widget system\n", "    init: function() {\n", "        // Get reference to Jupyter kernel with multiple fallback methods\n", "        this.kernel = null;\n", "\n", "        // Method 1: JupyterLab API\n", "        if (typeof window.jupyterapp !== 'undefined' && window.jupyterapp.shell) {\n", "            try {\n", "                const currentWidget = window.jupyterapp.shell.currentWidget;\n", "                console.log('Ju<PERSON>terLab currentWidget:', currentWidget);\n", "                if (currentWidget && currentWidget.context && currentWidget.context.sessionContext) {\n", "                    console.log('JupyterLab sessionContext:', currentWidget.context.sessionContext);\n", "                    const session = currentWidget.context.sessionContext.session;\n", "                    console.log('Ju<PERSON><PERSON>Lab session:', session);\n", "                    if (session && session.kernel) {\n", "                        this.kernel = session.kernel;\n", "                        console.log('Kernel found via JupyterLab API:', session.kernel);\n", "                    } else {\n", "                        console.log('JupyterLab session or kernel not available');\n", "                    }\n", "                } else {\n", "                    console.log('JupyterLab context or sessionContext not available');\n", "                }\n", "            } catch (e) {\n", "                console.log('JupyterLab API method failed:', e);\n", "            }\n", "        } else {\n", "            console.log('JupyterLab app not available');\n", "        }\n", "\n", "        // Method 2: Direct Jupyter reference (Classic Notebook)\n", "        if (!this.kernel && typeof Jupyter !== 'undefined' && Jupyter.notebook && Jupyter.notebook.kernel) {\n", "            this.kernel = Jupyter.notebook.kernel;\n", "            console.log('<PERSON><PERSON> found via Jupyter.notebook.kernel');\n", "        }\n", "\n", "        // Method 3: Try to find JupyterLab kernel through document\n", "        if (!this.kernel) {\n", "            try {\n", "                // Look for JupyterLab kernel in the document's scripts\n", "                const scripts = document.querySelectorAll('script');\n", "                for (let script of scripts) {\n", "                    if (script.textContent && script.textContent.includes('jupyter-widgets')) {\n", "                        // Try to access JupyterLab's kernel manager\n", "                        if (window.require) {\n", "                            window.require(['@jupyterlab/services'], function(services) {\n", "                                console.log('JupyterLab services available:', services);\n", "                            });\n", "                        }\n", "                        break;\n", "                    }\n", "                }\n", "            } catch (e) {\n", "                console.log('JupyterLab document search failed:', e);\n", "            }\n", "        }\n", "\n", "        // Method 4: Parent window <PERSON>\n", "        if (!this.kernel && typeof window.parent !== 'undefined' && window.parent.Jupyter &&\n", "                 window.parent.Jupyter.notebook && window.parent.Jupyter.notebook.kernel) {\n", "            this.kernel = window.parent.Jupyter.notebook.kernel;\n", "            console.log('Kernel found via parent window');\n", "        }\n", "\n", "        // Method 5: <PERSON>yt<PERSON> fallback\n", "        if (!this.kernel && typeof window.IPython !== 'undefined' && window.IPython.notebook &&\n", "                 window.IPython.notebook.kernel) {\n", "            this.kernel = window.IPython.notebook.kernel;\n", "            console.log('Kernel found via IPython.notebook.kernel');\n", "        }\n", "\n", "        // Method 6: Wait and retry\n", "        if (!this.kernel) {\n", "            console.log('<PERSON><PERSON> not immediately available, will retry...');\n", "            setTimeout(() => this.init(), 500);\n", "            return;\n", "        }\n", "\n", "        console.log('Logtalk Widgets initialized with kernel:', !!this.kernel);\n", "    },\n", "    \n", "    // Register a widget\n", "    registerWidget: function(widgetId, type, initialValue) {\n", "        this.widgets.set(widgetId, {\n", "            type: type,\n", "            value: initialValue,\n", "            element: document.getElementById(widgetId)\n", "        });\n", "    },\n", "    \n", "    // Update widget value and notify kernel\n", "    updateWidget: function(widgetId, value) {\n", "        if (this.widgets.has(widgetId)) {\n", "            const widget = this.widgets.get(widgetId);\n", "            widget.value = value;\n", "            \n", "            // Send update to kernel\n", "            this.sendWidgetUpdate(widgetId, value);\n", "        }\n", "    },\n", "    \n", "    // Send widget update to Logtalk kernel\n", "    sendWidgetUpdate: function(widgetId, value) {\n", "        // Properly escape the value for <PERSON><PERSON><PERSON>\n", "        let escapedValue;\n", "        if (typeof value === 'string') {\n", "            // Escape single quotes and backslashes for Logtalk string literals\n", "            escapedValue = value.replace(/\\\\/g, '\\\\\\\\').replace(/'/g, \"\\\\'\");\n", "            escapedValue = `'${escapedValue}'`;\n", "        } else if (typeof value === 'boolean') {\n", "            escapedValue = value ? 'true' : 'false';\n", "        } else {\n", "            escapedValue = String(value);\n", "        }\n", "\n", "        const code = `jupyter_widget_handling::set_widget_value('${widgetId}', ${escapedValue}).`;\n", "\n", "        console.log('Attempting to send widget update:', code);\n", "\n", "        // Try multiple execution methods in order of preference\n", "        const executionMethods = [\n", "            // Method 1: Use stored kernel reference\n", "            () => {\n", "                if (this.kernel) {\n", "                    this.kernel.execute(code, {\n", "                        silent: true,\n", "                        store_history: false,\n", "                        user_expressions: {},\n", "                        allow_stdin: false,\n", "                        stop_on_error: false\n", "                    });\n", "                    return true;\n", "                }\n", "                return false;\n", "            },\n", "\n", "            // Method 2: Direct Jupyter access\n", "            () => {\n", "                if (typeof Jupyter !== 'undefined' && Jupyter.notebook && Jupyter.notebook.kernel) {\n", "                    Jupyter.notebook.kernel.execute(code, {\n", "                        silent: true,\n", "                        store_history: false,\n", "                        user_expressions: {},\n", "                        allow_stdin: false,\n", "                        stop_on_error: false\n", "                    });\n", "                    this.kernel = Jupyter.notebook.kernel; // Store for future use\n", "                    return true;\n", "                }\n", "                return false;\n", "            },\n", "\n", "            // Method 3: Try parent window\n", "            () => {\n", "                if (typeof window.parent !== 'undefined' &&\n", "                    window.parent.Jupyter &&\n", "                    window.parent.Jupyter.notebook &&\n", "                    window.parent.Jupyter.notebook.kernel) {\n", "                    window.parent.Jupyter.notebook.kernel.execute(code, {\n", "                        silent: true,\n", "                        store_history: false,\n", "                        user_expressions: {},\n", "                        allow_stdin: false,\n", "                        stop_on_error: false\n", "                    });\n", "                    this.kernel = window.parent.Jupyter.notebook.kernel;\n", "                    return true;\n", "                }\n", "                return false;\n", "            },\n", "\n", "            // Method 4: <PERSON> fallback\n", "            () => {\n", "                if (typeof IPython !== 'undefined' && IPython.notebook && IPython.notebook.kernel) {\n", "                    IPython.notebook.kernel.execute(code, {\n", "                        silent: true,\n", "                        store_history: false,\n", "                        user_expressions: {},\n", "                        allow_stdin: false,\n", "                        stop_on_error: false\n", "                    });\n", "                    this.kernel = IPython.notebook.kernel;\n", "                    return true;\n", "                }\n", "                return false;\n", "            }\n", "        ];\n", "\n", "        // Try each method until one succeeds\n", "        for (let i = 0; i < executionMethods.length; i++) {\n", "            try {\n", "                if (executionMethods[i]()) {\n", "                    console.log(`Widget update sent successfully via method ${i + 1}`);\n", "                    return;\n", "                }\n", "            } catch (error) {\n", "                console.log(`Execution method ${i + 1} failed:`, error);\n", "            }\n", "        }\n", "\n", "        // If all methods fail, log error and store value locally\n", "        console.error('All kernel execution methods failed. Storing value locally.');\n", "        if (this.widgets.has(widgetId)) {\n", "            this.widgets.get(widgetId).value = value;\n", "            console.log(`Value stored locally for widget ${widgetId}:`, value);\n", "        }\n", "    },\n", "    \n", "    // Get widget value\n", "    getWidgetValue: function(widgetId) {\n", "        if (this.widgets.has(widgetId)) {\n", "            return this.widgets.get(widgetId).value;\n", "        }\n", "        return null;\n", "    },\n", "    \n", "    // Set widget value from kernel\n", "    setWidgetValue: function(widgetId, value) {\n", "        if (this.widgets.has(widgetId)) {\n", "            const widget = this.widgets.get(widgetId);\n", "            widget.value = value;\n", "            \n", "            // Update DOM element\n", "            const element = widget.element;\n", "            if (element) {\n", "                switch (widget.type) {\n", "                    case 'text_input':\n", "                    case 'number_input':\n", "                        element.value = value;\n", "                        break;\n", "                    case 'slider':\n", "                        element.value = value;\n", "                        // Update display value\n", "                        const valueDisplay = document.getElementById(widgetId + '_value');\n", "                        if (valueDisplay) {\n", "                            valueDisplay.textContent = value;\n", "                        }\n", "                        break;\n", "                    case 'dropdown':\n", "                        element.value = value;\n", "                        break;\n", "                    case 'checkbox':\n", "                        element.checked = (value === true || value === 'true');\n", "                        break;\n", "                }\n", "            }\n", "        }\n", "    },\n", "    \n", "    // Remove widget\n", "    removeWidget: function(widgetId) {\n", "        if (this.widgets.has(widgetId)) {\n", "            this.widgets.delete(widgetId);\n", "            \n", "            // Remove DOM element\n", "            const container = document.getElementById('container_' + widgetId);\n", "            if (container) {\n", "                container.remove();\n", "            }\n", "        }\n", "    },\n", "    \n", "    // Clear all widgets\n", "    clearAllWidgets: function() {\n", "        this.widgets.forEach((widget, widgetId) => {\n", "            this.removeWidget(widgetId);\n", "        });\n", "        this.widgets.clear();\n", "    },\n", "\n", "    // Debug function to check widget state\n", "    debugWidgets: function() {\n", "        console.log('Registered widgets:', this.widgets);\n", "        console.log('Kernel available:', !!this.kernel);\n", "        this.widgets.forEach((widget, widgetId) => {\n", "            console.log(`Widget ${widgetId}:`, {\n", "                type: widget.type,\n", "                value: widget.value,\n", "                element: widget.element,\n", "                elementValue: widget.element ? widget.element.value : 'N/A'\n", "            });\n", "        });\n", "    }\n", "};\n", "\n", "// Universal kernel execution function\n", "function executeInKernel(code, options = {}) {\n", "    console.log('Attempting to execute in kernel:', code);\n", "\n", "    const defaultOptions = {\n", "        silent: true,\n", "        store_history: false,\n", "        user_expressions: {},\n", "        allow_stdin: false,\n", "        stop_on_error: false,\n", "        ...options\n", "    };\n", "\n", "    // Try multiple kernel access methods\n", "    const kernelMethods = [\n", "        // Method 1: Standard Jupyter notebook\n", "        () => {\n", "            if (typeof Jupyter !== 'undefined' && Jupyter.notebook && Jupyter.notebook.kernel) {\n", "                Jupyter.notebook.kernel.execute(code, defaultOptions);\n", "                return 'Jupyter.notebook.kernel';\n", "            }\n", "            return null;\n", "        },\n", "\n", "        // Method 2: IPython fallback\n", "        () => {\n", "            if (typeof IPython !== 'undefined' && IPython.notebook && IPython.notebook.kernel) {\n", "                IPython.notebook.kernel.execute(code, defaultOptions);\n", "                return 'IPython.notebook.kernel';\n", "            }\n", "            return null;\n", "        },\n", "\n", "        // Method 3: Parent window access\n", "        () => {\n", "            if (window.parent && window.parent.Jupyter && window.parent.Jupyter.notebook && window.parent.Jupyter.notebook.kernel) {\n", "                window.parent.Jupyter.notebook.kernel.execute(code, defaultOptions);\n", "                return 'window.parent.Jupyter.notebook.kernel';\n", "            }\n", "            return null;\n", "        },\n", "\n", "        // Method 4: Top window access\n", "        () => {\n", "            if (window.top && window.top.Jupyter && window.top.Jupyter.notebook && window.top.Jupyter.notebook.kernel) {\n", "                window.top.Jupyter.notebook.kernel.execute(code, defaultOptions);\n", "                return 'window.top.Jupyter.notebook.kernel';\n", "            }\n", "            return null;\n", "        },\n", "\n", "        // Method 5: VS Code notebook API\n", "        () => {\n", "            if (typeof acquireVsCodeApi !== 'undefined') {\n", "                try {\n", "                    const vscode = acquireVsCodeApi();\n", "                    // VS Code has a different API - we'll need to post a message\n", "                    vscode.postMessage({\n", "                        type: 'executeCode',\n", "                        code: code\n", "                    });\n", "                    return 'VS Code API';\n", "                } catch (e) {\n", "                    console.log('VS Code API failed:', e);\n", "                }\n", "            }\n", "            return null;\n", "        },\n", "\n", "        // Method 6: <PERSON><PERSON><PERSON><PERSON>ab context\n", "        () => {\n", "            console.log('Trying JupyterLab execution method...');\n", "            // Try to find JupyterLab kernel through various paths\n", "            const labPaths = [\n", "                'window.jup<PERSON><PERSON><PERSON>',\n", "                'window.parent.jup<PERSON><PERSON><PERSON>',\n", "                'window.top.jup<PERSON><PERSON>p'\n", "            ];\n", "\n", "            for (const path of labPaths) {\n", "                try {\n", "                    console.log(`Trying JupyterLab path: ${path}`);\n", "                    const app = eval(path);\n", "                    console.log(`App found at ${path}:`, app);\n", "                    if (app && app.shell && app.shell.currentWidget) {\n", "                        const widget = app.shell.currentWidget;\n", "                        console.log(`Current widget:`, widget);\n", "                        if (widget.context && widget.context.sessionContext && widget.context.sessionContext.session) {\n", "                            const kernel = widget.context.sessionContext.session.kernel;\n", "                            console.log(`<PERSON><PERSON> found:`, kernel);\n", "                            if (kernel && kernel.requestExecute) {\n", "                                console.log(`Executing code via JupyterLab: ${code}`);\n", "                                const future = kernel.requestExecute({ code: code });\n", "                                console.log('JupyterLab execution future:', future);\n", "                                return `<PERSON><PERSON><PERSON><PERSON><PERSON> via ${path}`;\n", "                            } else {\n", "                                console.log('Kernel or requestExecute not available');\n", "                            }\n", "                        } else {\n", "                            console.log('Context, sessionContext, or session not available');\n", "                        }\n", "                    } else {\n", "                        console.log('App, shell, or currentWidget not available');\n", "                    }\n", "                } catch (e) {\n", "                    console.log(`JupyterLab method ${path} failed:`, e);\n", "                }\n", "            }\n", "            console.log('All JupyterLab paths failed');\n", "            return null;\n", "        },\n", "\n", "        // Method 7: JupyterLab direct kernel access\n", "        () => {\n", "            if (typeof window.jupyterapp !== 'undefined') {\n", "                try {\n", "                    const app = window.jupyterapp;\n", "                    const currentWidget = app.shell.currentWidget;\n", "                    if (currentWidget && currentWidget.context) {\n", "                        const sessionContext = currentWidget.context.sessionContext;\n", "                        if (sessionContext && sessionContext.session && sessionContext.session.kernel) {\n", "                            const kernel = sessionContext.session.kernel;\n", "                            const future = kernel.requestExecute({ code: code });\n", "                            console.log('<PERSON><PERSON><PERSON><PERSON><PERSON> direct execution:', future);\n", "                            return '<PERSON><PERSON><PERSON><PERSON><PERSON> direct';\n", "                        }\n", "                    }\n", "                } catch (e) {\n", "                    console.log('Jupyter<PERSON>ab direct method failed:', e);\n", "                }\n", "            }\n", "            return null;\n", "        }\n", "    ];\n", "\n", "    // Try each method until one succeeds\n", "    for (let i = 0; i < kernelMethods.length; i++) {\n", "        try {\n", "            const result = kernelMethods[i]();\n", "            if (result) {\n", "                console.log(`✅ Code executed successfully via: ${result}`);\n", "                return true;\n", "            }\n", "        } catch (error) {\n", "            console.log(`❌ Kernel method ${i + 1} failed:`, error);\n", "        }\n", "    }\n", "\n", "    console.error('❌ All kernel execution methods failed');\n", "    return false;\n", "}\n", "\n", "// Global function for widget updates (called from HTML)\n", "function updateLogtalkWidget(widgetId, value) {\n", "    console.log('updateLogtalkWidget called:', widgetId, value);\n", "\n", "    // Properly escape the value for <PERSON><PERSON><PERSON>\n", "    let escapedValue;\n", "    if (typeof value === 'string') {\n", "        escapedValue = `'${value.replace(/\\\\/g, '\\\\\\\\').replace(/'/g, \"\\\\'\")}'`;\n", "    } else if (typeof value === 'boolean') {\n", "        escapedValue = value ? 'true' : 'false';\n", "    } else {\n", "        escapedValue = String(value);\n", "    }\n", "\n", "    const code = `jupyter_widget_handling::set_widget_value('${widgetId}', ${escapedValue}).`;\n", "\n", "    // Try universal kernel execution\n", "    const success = executeInKernel(code);\n", "\n", "    if (success) {\n", "        console.log('✅ Widget update sent successfully');\n", "\n", "        // Also update local state if LogtalkWidgets is available\n", "        if (typeof LogtalkWidgets !== 'undefined' && LogtalkWidgets.widgets && LogtalkWidgets.widgets.has(widgetId)) {\n", "            LogtalkWidgets.widgets.get(widgetId).value = value;\n", "        }\n", "    } else {\n", "        console.error('❌ Widget update failed - storing locally only');\n", "\n", "        // Store locally as fallback\n", "        if (typeof LogtalkWidgets !== 'undefined') {\n", "            if (!LogtalkWidgets.widgets) {\n", "                LogtalkWidgets.widgets = new Map();\n", "            }\n", "            if (!LogtalkWidgets.widgets.has(widgetId)) {\n", "                LogtalkWidgets.widgets.set(widgetId, { type: 'unknown', value: value });\n", "            } else {\n", "                LogtalkWidgets.widgets.get(widgetId).value = value;\n", "            }\n", "            console.log('Value stored locally for widget:', widgetId, value);\n", "        }\n", "    }\n", "}\n", "\n", "// Auto-register widgets when they are created\n", "function autoRegisterWidget(widgetId, type, initialValue) {\n", "    LogtalkWidgets.registerWidget(widgetId, type, initialValue);\n", "}\n", "\n", "// Manual kernel connection function for debugging\n", "function connectLogtalkWidgetsToKernel() {\n", "    console.log('Attempting manual kernel connection...');\n", "\n", "    // Try all possible kernel references\n", "    const kernelSources = [\n", "        () => Jupyter && Jupyter.notebook && Jupyter.notebook.kernel,\n", "        () => IPython && IPython.notebook && IPython.notebook.kernel,\n", "        () => window.parent.Jupyter && window.parent.Jupyter.notebook && window.parent.Jupyter.notebook.kernel,\n", "        () => window.parent.IPython && window.parent.IPython.notebook && window.parent.IPython.notebook.kernel\n", "    ];\n", "\n", "    for (let i = 0; i < kernelSources.length; i++) {\n", "        try {\n", "            const kernel = kernelSources[i]();\n", "            if (kernel) {\n", "                LogtalkWidgets.kernel = kernel;\n", "                console.log(`Kernel connected via method ${i + 1}:`, kernel);\n", "                return true;\n", "            }\n", "        } catch (e) {\n", "            console.log(`Kernel connection method ${i + 1} failed:`, e);\n", "        }\n", "    }\n", "\n", "    console.log('All kernel connection methods failed');\n", "    return false;\n", "}\n", "\n", "// Make functions globally available for debugging\n", "window.connectLogtalkWidgetsToKernel = connectLogtalkWidgetsToKernel;\n", "window.LogtalkWidgets = LogtalkWidgets;\n", "\n", "// Multiple initialization strategies\n", "function initializeWidgets() {\n", "    LogtalkWidgets.init();\n", "\n", "    // Keep trying until kernel is available\n", "    if (!LogtalkWidgets.kernel) {\n", "        setTimeout(initializeWidgets, 1000);\n", "    }\n", "}\n", "\n", "// Initialize when DOM is ready\n", "if (document.readyState === 'loading') {\n", "    document.addEventListener('DOMContentLoaded', initializeWidgets);\n", "} else {\n", "    initializeWidgets();\n", "}\n", "\n", "// Initialize when <PERSON><PERSON><PERSON> is ready (for notebook environment)\n", "if (typeof Jupyter !== 'undefined') {\n", "    // Try multiple Jupyter events\n", "    if (Jupyter.notebook && Jupyter.notebook.events) {\n", "        Jupyter.notebook.events.on('kernel_ready.Kernel', function() {\n", "            console.log('Kernel ready event fired');\n", "            LogtalkWidgets.init();\n", "        });\n", "\n", "        Jupyter.notebook.events.on('kernel_connected.Kernel', function() {\n", "            console.log('Kernel connected event fired');\n", "            LogtalkWidgets.init();\n", "        });\n", "    }\n", "}\n", "\n", "// Fallback: Keep trying to initialize\n", "setTimeout(function() {\n", "    if (!LogtalkWidgets.kernel) {\n", "        console.log('Fallback initialization attempt');\n", "        LogtalkWidgets.init();\n", "    }\n", "}, 2000);\n", "\n", "// CSS styles for widgets\n", "const widgetStyles = `\n", "<style>\n", ".logtalk-widget {\n", "    margin: 10px 0;\n", "    padding: 10px;\n", "    border: 1px solid #e0e0e0;\n", "    border-radius: 5px;\n", "    background-color: #fafafa;\n", "    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n", "}\n", "\n", ".logtalk-widget label {\n", "    font-weight: 500;\n", "    color: #333;\n", "    margin-bottom: 5px;\n", "    display: inline-block;\n", "}\n", "\n", ".logtalk-widget input[type=\"text\"],\n", ".logtalk-widget input[type=\"number\"],\n", ".logtalk-widget select {\n", "    width: 200px;\n", "    padding: 6px 10px;\n", "    border: 1px solid #ccc;\n", "    border-radius: 4px;\n", "    font-size: 14px;\n", "    transition: border-color 0.3s ease;\n", "}\n", "\n", ".logtalk-widget input[type=\"text\"]:focus,\n", ".logtalk-widget input[type=\"number\"]:focus,\n", ".logtalk-widget select:focus {\n", "    outline: none;\n", "    border-color: #007cba;\n", "    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2);\n", "}\n", "\n", ".logtalk-widget input[type=\"range\"] {\n", "    width: 200px;\n", "    margin: 5px 0;\n", "}\n", "\n", ".logtalk-widget input[type=\"checkbox\"] {\n", "    margin-right: 8px;\n", "    transform: scale(1.2);\n", "}\n", "\n", ".logtalk-widget button {\n", "    background-color: #007cba;\n", "    color: white;\n", "    border: none;\n", "    padding: 8px 16px;\n", "    border-radius: 4px;\n", "    cursor: pointer;\n", "    font-size: 14px;\n", "    transition: background-color 0.3s ease;\n", "}\n", "\n", ".logtalk-widget button:hover {\n", "    background-color: #005a87;\n", "}\n", "\n", ".logtalk-widget button:active {\n", "    background-color: #004a73;\n", "}\n", "</style>\n", "`;\n", "\n", "// Inject CSS styles\n", "if (document.head) {\n", "    document.head.insertAdjacentHTML('beforeend', widgetStyles);\n", "}\n", "\n", "                console.log('Logtalk widget library loaded');\n", "            } else {\n", "                console.log('Logtalk widget library already available');\n", "            }\n", "\n", "            // Ensure kernel reference is updated\n", "            if (typeof LogtalkWidgets !== 'undefined') {\n", "                LogtalkWidgets.init();\n", "            }\n", "        </script>\n", "        "], "text/plain": ["Logtalk Widget"]}, "metadata": {"text/html": {"isolated": false}}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["% This will include JavaScript to manually test kernel execution\n", "jupyter_term_handling::assert_success_response(kernel_test, [], '', [widget_html-'<script>console.log(\"Testing manual kernel execution...\"); function testJupyterLabKernel() { if (typeof window.jupyterapp !== \"undefined\" && window.jupyterapp.shell) { try { const currentWidget = window.jupyterapp.shell.currentWidget; if (currentWidget && currentWidget.context && currentWidget.context.sessionContext) { const session = currentWidget.context.sessionContext.session; if (session && session.kernel) { console.log(\"Found JupyterLab kernel:\", session.kernel); const testCode = \"write(\\'Manual kernel test successful!\\'), nl.\"; console.log(\"Executing test code:\", testCode); const future = session.kernel.requestExecute({ code: testCode }); console.log(\"Execution future:\", future); return true; } } } catch (e) { console.error(\"JupyterLab kernel test failed:\", e); } } console.log(\"JupyterLab kernel not available\"); return false; } const result = testJupyterLabKernel(); console.log(\"Manual kernel test result:\", result);</script>'])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Check Widget Value\n", "\n", "After interacting with the widget above, check if the value was updated:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["Widget value: Hello JupyterLab"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1mValue = 'Hello JupyterLab'"]}, "metadata": {}, "output_type": "display_data"}], "source": ["jupyter::get_widget_value(lab_test_widget, Value),\n", "format('Widget value: ~w~n', [Value])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Debug Widget State\n", "\n", "Check the server-side widget state:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["=== Widget Debug Information ===\n", "Widget lab_test_widget: Type=text_input, Value=Hello JupyterLab"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[1;31mfalse"]}, "metadata": {}, "output_type": "display_data"}], "source": ["jupyter::debug_widgets."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Manual Widget Update Test\n", "\n", "Test updating widget value from JavaScript console:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["% This provides a manual test function\n", "jupyter_term_handling::assert_success_response(manual_test, [], '', [widget_html-'<script>window.manualWidgetTest = function() { console.log(\"Manual widget test function called\"); if (typeof window.jupyterapp !== \"undefined\" && window.jupyterapp.shell) { try { const currentWidget = window.jupyterapp.shell.currentWidget; if (currentWidget && currentWidget.context && currentWidget.context.sessionContext) { const session = currentWidget.context.sessionContext.session; if (session && session.kernel) { const testCode = \"jupyter_widget_handling::set_widget_value(lab_test_widget, \\'manual_test_value\\').\"; console.log(\"Executing manual update:\", testCode); const future = session.kernel.requestExecute({ code: testCode }); console.log(\"Manual update future:\", future); return true; } } } catch (e) { console.error(\"Manual test failed:\", e); } } return false; }; console.log(\"Manual test function available as window.manualWidgetTest()\");</script>'])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Instructions for Testing\n", "\n", "1. **Run all cells above**\n", "2. **Open browser console** (F12)\n", "3. **Look for debug output** from the environment detection\n", "4. **Try interacting** with the text widget\n", "5. **Run the manual test** by typing in console: `window.manualWidgetTest()`\n", "6. **Check if values update** by running Step 4 again\n", "\n", "## Expected Console Output\n", "\n", "You should see:\n", "- Environment detection showing JupyterLab objects\n", "- Kernel found via JupyterLab API\n", "- Manual kernel test successful\n", "- Widget update confirmations"]}], "metadata": {"kernelspec": {"display_name": "Logtalk", "language": "logtalk", "name": "logtalk_kernel"}, "language_info": {"codemirror_mode": "logtalk", "file_extension": ".lgt", "mimetype": "text/x-logtalk", "name": "Logtalk"}}, "nbformat": 4, "nbformat_minor": 4}