{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ju<PERSON>terLab Widget Test\n", "\n", "This notebook tests HTML/JavaScript widgets specifically in JupyterLab environment."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Environment Detection\n", "\n", "First, let's check what's available in the JupyterLab environment:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["% This will include JavaScript to detect the environment\n", "jupyter_response_handling::assert_success_response(debug, [], '', [widget_html-'<script>console.log(\"=== JupyterLab Environment Debug ===\"); console.log(\"window.jupyterapp:\", typeof window.jupyterapp); console.log(\"Jupyter:\", typeof Jupyter); console.log(\"IPython:\", typeof IPython); if (typeof window.jupyterapp !== \"undefined\") { console.log(\"JupyterLab app:\", window.jupyterapp); console.log(\"Current widget:\", window.jupyterapp.shell ? window.jupyterapp.shell.currentWidget : \"no shell\"); if (window.jupyterapp.shell && window.jupyterapp.shell.currentWidget) { const widget = window.jupyterapp.shell.currentWidget; console.log(\"Widget context:\", widget.context); if (widget.context && widget.context.sessionContext) { console.log(\"Session context:\", widget.context.sessionContext); console.log(\"Session:\", widget.context.sessionContext.session); if (widget.context.sessionContext.session) { console.log(\"Kernel:\", widget.context.sessionContext.session.kernel); } } } } console.log(\"=== End Debug ===\");</script>'])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Test Widget Creation\n", "\n", "Create a simple text widget:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::create_text_input(lab_test_widget, 'Test in JupyterLab:', 'Hello JupyterLab')."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Manual <PERSON>\n", "\n", "Test kernel execution manually:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["% This will include JavaScript to manually test kernel execution\n", "jupyter_response_handling::assert_success_response(kernel_test, [], '', [widget_html-'<script>console.log(\"Testing manual kernel execution...\"); function testJupyterLabKernel() { if (typeof window.jupyterapp !== \"undefined\" && window.jupyterapp.shell) { try { const currentWidget = window.jupyterapp.shell.currentWidget; if (currentWidget && currentWidget.context && currentWidget.context.sessionContext) { const session = currentWidget.context.sessionContext.session; if (session && session.kernel) { console.log(\"Found JupyterLab kernel:\", session.kernel); const testCode = \"write(\\'Manual kernel test successful!\\'), nl.\"; console.log(\"Executing test code:\", testCode); const future = session.kernel.requestExecute({ code: testCode }); console.log(\"Execution future:\", future); return true; } } } catch (e) { console.error(\"JupyterLab kernel test failed:\", e); } } console.log(\"JupyterLab kernel not available\"); return false; } const result = testJupyterLabKernel(); console.log(\"Manual kernel test result:\", result);</script>'])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Check Widget Value\n", "\n", "After interacting with the widget above, check if the value was updated:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::get_widget_value(lab_test_widget, Value),\n", "format('Widget value: ~w~n', [Value])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Debug Widget State\n", "\n", "Check the server-side widget state:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::debug_widgets."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Manual Widget Update Test\n", "\n", "Test updating widget value from JavaScript console:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["% This provides a manual test function\n", "jupyter_response_handling::assert_success_response(manual_test, [], '', [widget_html-'<script>window.manualWidgetTest = function() { console.log(\"Manual widget test function called\"); if (typeof window.jupyterapp !== \"undefined\" && window.jupyterapp.shell) { try { const currentWidget = window.jupyterapp.shell.currentWidget; if (currentWidget && currentWidget.context && currentWidget.context.sessionContext) { const session = currentWidget.context.sessionContext.session; if (session && session.kernel) { const testCode = \"jupyter_widget_handling::set_widget_value(lab_test_widget, \\'manual_test_value\\').\"; console.log(\"Executing manual update:\", testCode); const future = session.kernel.requestExecute({ code: testCode }); console.log(\"Manual update future:\", future); return true; } } } catch (e) { console.error(\"Manual test failed:\", e); } } return false; }; console.log(\"Manual test function available as window.manualWidgetTest()\");</script>'])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Instructions for Testing\n", "\n", "1. **Run all cells above**\n", "2. **Open browser console** (F12)\n", "3. **Look for debug output** from the environment detection\n", "4. **Try interacting** with the text widget\n", "5. **Run the manual test** by typing in console: `window.manualWidgetTest()`\n", "6. **Check if values update** by running Step 4 again\n", "\n", "## Expected Console Output\n", "\n", "You should see:\n", "- Environment detection showing JupyterLab objects\n", "- Kernel found via JupyterLab API\n", "- Manual kernel test successful\n", "- Widget update confirmations"]}], "metadata": {"kernelspec": {"display_name": "Logtalk", "language": "logtalk", "name": "logtalk"}, "language_info": {"codemirror_mode": "prolog", "file_extension": ".lgt", "mimetype": "text/x-logtalk", "name": "logtalk", "pygments_lexer": "prolog", "version": "3.0"}}, "nbformat": 4, "nbformat_minor": 4}