Flag	Value
linter	default
always_true_or_false_goals	warning
arithmetic_expressions	warning
catchall_catch	silent
conditionals	warning
deprecated	warning
disjunctions	warning
duplicated_clauses	silent
duplicated_directives	warning
encodings	warning
general	warning
grammar_rules	warning
lambda_variables	warning
left_recursion	warning
missing_directives	warning
naming	silent
portability	silent
redefined_built_ins	silent
redefined_operators	warning
singleton_variables	warning
steadfastness	silent
suspicious_calls	warning
tail_recursive	silent
trivial_goal_fails	warning
undefined_predicates	warning
unknown_entities	warning
unknown_predicates	warning
complements	deny
context_switching_calls	allow
dynamic_declarations	deny
events	deny
clean	on
code_prefix	$
debug	off
optimize	off
reload	changed
report	warnings
scratch_directory	'/private/var/folders/w9/0h2yhwgh8xj42v0059bpn5h00000gn/T/logtalk70943/'
source_data	on
version_data	logtalk(3,90,0,b01)
settings_file	allow
prolog_compatible_version	@>=(v(10,0,0))
prolog_dialect	xvm
prolog_version	v(10,2,2)
underscore_variables	dont_care
coinduction	unsupported
encoding_directive	source
engines	supported
modules	unsupported
tabling	unsupported
threads	supported
unicode	full
prolog_compiler	[]
prolog_loader	[]
suppress_path_prefix	''
