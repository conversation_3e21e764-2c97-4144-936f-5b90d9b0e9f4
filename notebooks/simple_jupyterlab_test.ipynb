{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Simple JupyterLab Widget Test\n", "\n", "A safe test that won't crash the kernel."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Create a Simple Widget"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::create_text_input(simple_test, 'Type here:', 'Hello JupyterLab')."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Check Initial Value"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::get_widget_value(simple_test, Value),\n", "format('Initial value: ~w~n', [Value])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Manual Value Update"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter_widget_handling::set_widget_value(simple_test, 'manually_updated_value')."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Check Updated Value"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::get_widget_value(simple_test, Value),\n", "format('Updated value: ~w~n', [Value])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Instructions\n", "\n", "1. Run all cells above\n", "2. Try typing in the text widget that appears\n", "3. Open browser console (F12) and look for messages\n", "4. Re-run Step 4 to see if the value changed\n", "\n", "If the widget appears but typing doesn't update the value, we have a kernel communication issue.\n", "If the widget doesn't appear at all, we have a rendering issue."]}], "metadata": {"kernelspec": {"display_name": "Logtalk", "language": "logtalk", "name": "logtalk"}, "language_info": {"codemirror_mode": "prolog", "file_extension": ".lgt", "mimetype": "text/x-logtalk", "name": "logtalk", "pygments_lexer": "prolog", "version": "3.0"}}, "nbformat": 4, "nbformat_minor": 4}