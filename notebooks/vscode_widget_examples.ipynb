{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# VS Code Compatible Widget Examples\n",
    "\n",
    "This notebook demonstrates data input widgets that work in VS Code with the Logtalk kernel.\n",
    "\n",
    "Since VS Code doesn't support HTML/JavaScript execution in non-Python kernels, these widgets use interactive text prompts instead."
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Text Input Widget\n",
    "\n",
    "Prompt for text input with a default value:"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "jupyter::prompt_text_input(user_name, 'What is your name?', '<PERSON>')."
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "Get the entered value:"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "jupyter_vscode_widgets::get_widget_value(user_name, Name),\n",
    "format('Hello, ~w!~n', [Name])."
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Number Input Widget\n",
    "\n",
    "Prompt for numeric input:"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "jupyter::prompt_number_input(user_age, 'What is your age?', 25)."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "jupyter_vscode_widgets::get_widget_value(user_age, Age),\n",
    "format('You are ~w years old.~n', [Age])."
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Choice Widget\n",
    "\n",
    "Prompt for selection from multiple options:"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "jupyter::prompt_choice(favorite_color, 'What is your favorite color?', [red, green, blue, yellow, purple])."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "jupyter_vscode_widgets::get_widget_value(favorite_color, Color),\n",
    "format('Your favorite color is ~w.~n', [Color])."
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Yes/No Widget\n",
    "\n",
    "Prompt for boolean input:"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "jupyter::prompt_yes_no(likes_programming, 'Do you like programming?')."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "jupyter_vscode_widgets::get_widget_value(likes_programming, Answer),\n",
    "(   Answer == true ->\n",
    "    write('Great! Programming is awesome!')\n",
    ";   write('That\\'s okay, everyone has different interests.')\n",
    "), nl."
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Working with Multiple Widgets\n",
    "\n",
    "You can collect multiple pieces of information and use them together:"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "% Collect user information\n",
    "collect_user_info :-\n",
    "    jupyter::prompt_text_input(full_name, 'Enter your full name:', 'Anonymous'),\n",
    "    jupyter::prompt_number_input(birth_year, 'Enter your birth year:', 1990),\n",
    "    jupyter::prompt_choice(country, 'Select your country:', [usa, canada, uk, germany, france, other]),\n",
    "    jupyter::prompt_yes_no(newsletter, 'Subscribe to newsletter?')."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "collect_user_info."
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "Display collected information:"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "display_user_info :-\n",
    "    jupyter_vscode_widgets::get_widget_value(full_name, Name),\n",
    "    jupyter_vscode_widgets::get_widget_value(birth_year, BirthYear),\n",
    "    jupyter_vscode_widgets::get_widget_value(country, Country),\n",
    "    jupyter_vscode_widgets::get_widget_value(newsletter, Newsletter),\n",
    "    \n",
    "    get_time(time(Year, _, _, _, _, _, _, _, _)),\n",
    "    Age is Year - BirthYear,\n",
    "    \n",
    "    format('=== USER PROFILE ===~n', []),\n",
    "    format('Name: ~w~n', [Name]),\n",
    "    format('Age: ~w years old~n', [Age]),\n",
    "    format('Country: ~w~n', [Country]),\n",
    "    format('Newsletter: ~w~n', [Newsletter]),\n",
    "    format('==================~n', [])."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "display_user_info."
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Widget Management\n",
    "\n",
    "List all current widget values:"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "jupyter::list_widgets."
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "Clear all widget values:"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "jupyter::clear_widgets."
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Advanced Example: Survey System\n",
    "\n",
    "Create a complete survey system:"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "% Programming experience survey\n",
    "programming_survey :-\n",
    "    format('=== PROGRAMMING EXPERIENCE SURVEY ===~n', []),\n",
    "    jupyter::prompt_text_input(participant_id, 'Enter participant ID:', 'P001'),\n",
    "    jupyter::prompt_choice(experience_level, 'Programming experience level:', [beginner, intermediate, advanced, expert]),\n",
    "    jupyter::prompt_number_input(years_coding, 'Years of programming experience:', 0),\n",
    "    jupyter::prompt_choice(primary_language, 'Primary programming language:', [python, java, javascript, cpp, csharp, prolog, logtalk, other]),\n",
    "    jupyter::prompt_yes_no(enjoys_coding, 'Do you enjoy programming?'),\n",
    "    jupyter::prompt_choice(preferred_paradigm, 'Preferred programming paradigm:', [procedural, object_oriented, functional, logic, declarative]),\n",
    "    format('Survey completed! Thank you for participating.~n', [])."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "programming_survey."
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "Analyze survey results:"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "analyze_survey :-\n",
    "    jupyter_vscode_widgets::get_widget_value(participant_id, ID),\n",
    "    jupyter_vscode_widgets::get_widget_value(experience_level, Level),\n",
    "    jupyter_vscode_widgets::get_widget_value(years_coding, Years),\n",
    "    jupyter_vscode_widgets::get_widget_value(primary_language, Language),\n",
    "    jupyter_vscode_widgets::get_widget_value(enjoys_coding, Enjoys),\n",
    "    jupyter_vscode_widgets::get_widget_value(preferred_paradigm, Paradigm),\n",
    "    \n",
    "    format('=== SURVEY ANALYSIS ===~n', []),\n",
    "    format('Participant: ~w~n', [ID]),\n",
    "    format('Experience: ~w (~w years)~n', [Level, Years]),\n",
    "    format('Primary Language: ~w~n', [Language]),\n",
    "    format('Enjoys Programming: ~w~n', [Enjoys]),\n",
    "    format('Preferred Paradigm: ~w~n', [Paradigm]),\n",
    "    \n",
    "    % Simple analysis\n",
    "    (   Years > 5 ->\n",
    "        format('Analysis: Experienced programmer~n', [])\n",
    "    ;   Years > 2 ->\n",
    "        format('Analysis: Intermediate programmer~n', [])\n",
    "    ;   format('Analysis: Beginning programmer~n', [])\n",
    "    ),\n",
    "    \n",
    "    (   Language == logtalk ->\n",
    "        format('Special note: Logtalk programmer detected! 🎉~n', [])\n",
    "    ;   Language == prolog ->\n",
    "        format('Special note: Logic programming enthusiast! 👍~n', [])\n",
    "    ;   true\n",
    "    ),\n",
    "    \n",
    "    format('=====================~n', [])."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "analyze_survey."
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Logtalk",\n",
   "language": "logtalk",\n",
   "name": "logtalk"\n  },\n  "language_info": {\n   "codemirror_mode": "prolog",\n   "file_extension": ".lgt",\n   "mimetype": "text/x-logtalk",\n   "name": "logtalk",\n   "pygments_lexer": "prolog",\n   "version": "3.0"\n  }\n },\n "nbformat": 4,\n "nbformat_minor": 4\n}
