{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# VS Code Compatible Widget Examples\\n", "\\n", "This notebook demonstrates data input widgets that work in VS Code with the Logtalk kernel.\\n", "\\n", "Since VS Code doesn't support HTML/JavaScript execution in non-Python kernels, these widgets use interactive text prompts instead."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Text Input Widget\\n", "\\n", "Prompt for text input with a default value:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::prompt_text_input(user_name, 'What is your name?', '<PERSON>')."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Get the entered value:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter_vscode_widgets::get_widget_value(user_name, Name),\\n", "format('Hello, ~w!~n', [Name])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Number Input Widget\\n", "\\n", "Prompt for numeric input:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::prompt_number_input(user_age, 'What is your age?', 25)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter_vscode_widgets::get_widget_value(user_age, Age),\\n", "format('You are ~w years old.~n', [Age])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Choice Widget\\n", "\\n", "Prompt for selection from multiple options:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::prompt_choice(favorite_color, 'What is your favorite color?', [red, green, blue, yellow, purple])."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter_vscode_widgets::get_widget_value(favorite_color, Color),\\n", "format('Your favorite color is ~w.~n', [Color])."]}], "metadata": {"kernelspec": {"display_name": "Logtalk", "language": "logtalk", "name": "logtalk"}, "language_info": {"codemirror_mode": "prolog", "file_extension": ".lgt", "mimetype": "text/x-logtalk", "name": "logtalk", "pygments_lexer": "prolog", "version": "3.0"}}, "nbformat": 4, "nbformat_minor": 4}