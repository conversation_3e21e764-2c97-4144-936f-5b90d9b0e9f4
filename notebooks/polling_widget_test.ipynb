{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Polling-Based Widget System\n", "\n", "A kernel-safe approach where widgets store values in localStorage and the server polls for updates."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Create Simple Widgets"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::create_text_input(poll_test1, 'Widget 1:', 'initial1'),\n", "jupyter::create_text_input(poll_test2, 'Widget 2:', 'initial2'),\n", "jupyter::create_text_input(poll_test3, 'Widget 3:', 'initial3')."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Check Initial Values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::get_widget_value(poll_test1, V1),\n", "jupyter::get_widget_value(poll_test2, V2),\n", "jupyter::get_widget_value(poll_test3, V3),\n", "format('Initial values: ~w, ~w, ~w~n', [V1, V2, V3])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Interact with Widgets\n", "\n", "1. **Change the values** in all three widgets above\n", "2. **Click outside each widget** or press Tab to trigger the `onchange` event\n", "3. **Check browser console** (F12) - you should see \"Stored widget\" messages\n", "\n", "The widgets now use simple `onchange` events that won't crash the kernel."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Auto-Sync Widgets\n", "\n", "**Run this cell to automatically sync all widget values:**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::sync_widgets."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Check Updated Values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::get_widget_value(poll_test1, V1),\n", "jupyter::get_widget_value(poll_test2, V2),\n", "jupyter::get_widget_value(poll_test3, V3),\n", "format('Updated values: ~w, ~w, ~w~n', [V1, V2, V3])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Interactive Workflow Test\n", "\n", "Let's test a more realistic workflow:\n", "\n", "1. **Change widget values again**\n", "2. **Run the sync command**\n", "3. **Process the values**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["% Sync and process in one step\n", "jupyter::sync_widgets,\n", "jupyter::get_widget_value(poll_test1, Name),\n", "jupyter::get_widget_value(poll_test2, City),\n", "jupyter::get_widget_value(poll_test3, <PERSON><PERSON>),\n", "format('=== FORM PROCESSING ===~n', []),\n", "format('Name: ~w~n', [Name]),\n", "format('City: ~w~n', [City]),\n", "format('Hobby: ~w~n', [Ho<PERSON>]),\n", "format('======================~n', [])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## How This Works\n", "\n", "### Widget Side (JavaScript):\n", "- **Simple `onchange` events** - No complex JavaScript\n", "- **localStorage storage** - Reliable, kernel-safe\n", "- **Console logging** - Easy to debug\n", "\n", "### Server Side (Logtalk):\n", "- **`jupyter::sync_widgets`** - Polls localStorage for updates\n", "- **Automatic cleanup** - Removes synced values from localStorage\n", "- **Batch processing** - Syncs all widgets at once\n", "\n", "## Benefits\n", "\n", "- ✅ **Kernel-safe** - No complex JavaScript that breaks JSON\n", "- ✅ **Reliable** - Simple onchange events always work\n", "- ✅ **Batch sync** - One command syncs all widgets\n", "- ✅ **Debuggable** - Clear console logging\n", "- ✅ **Flexible** - Can sync on demand or periodically\n", "\n", "## Workflow\n", "\n", "```\n", "1. User changes widget values\n", "2. Values stored in localStorage automatically\n", "3. User runs: jupyter::sync_widgets\n", "4. All widget values sync to server\n", "5. User processes values with get_widget_value\n", "```\n", "\n", "## Future Enhancements\n", "\n", "This foundation enables:\n", "- **Periodic auto-sync** - Timer-based polling\n", "- **Selective sync** - Sync specific widgets only\n", "- **Change detection** - Only sync modified values\n", "- **Form validation** - Check values before sync\n", "\n", "**This approach provides true widget functionality while staying completely kernel-safe!**"]}], "metadata": {"kernelspec": {"display_name": "Logtalk", "language": "logtalk", "name": "logtalk"}, "language_info": {"codemirror_mode": "prolog", "file_extension": ".lgt", "mimetype": "text/x-logtalk", "name": "logtalk", "pygments_lexer": "prolog", "version": "3.0"}}, "nbformat": 4, "nbformat_minor": 4}