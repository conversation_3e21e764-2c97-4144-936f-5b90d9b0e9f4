{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# JupyterLab Debug Test\n", "\n", "This will help us see exactly what's available in your JupyterLab environment."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Environment Inspection"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["% Create debug HTML with detailed environment inspection\n", "jupyter_term_handling::assert_success_response(debug, [], '', [\n", "    widget_html-'<script>\n", "console.log(\"=== DETAILED JUPYTERLAB DEBUG ===\");\n", "console.log(\"window.jupyterapp:\", window.jupyterapp);\n", "console.log(\"typeof window.jupyterapp:\", typeof window.jupyterapp);\n", "\n", "if (typeof window.jupyterapp !== \"undefined\") {\n", "    console.log(\"jupyterapp.shell:\", window.jupyterapp.shell);\n", "    \n", "    if (window.jupyterapp.shell) {\n", "        console.log(\"shell.currentWidget:\", window.jupyterapp.shell.currentWidget);\n", "        \n", "        const widget = window.jupyterapp.shell.currentWidget;\n", "        if (widget) {\n", "            console.log(\"widget.context:\", widget.context);\n", "            \n", "            if (widget.context) {\n", "                console.log(\"context.sessionContext:\", widget.context.sessionContext);\n", "                \n", "                if (widget.context.sessionContext) {\n", "                    console.log(\"sessionContext.session:\", widget.context.sessionContext.session);\n", "                    \n", "                    if (widget.context.sessionContext.session) {\n", "                        const session = widget.context.sessionContext.session;\n", "                        console.log(\"session.kernel:\", session.kernel);\n", "                        console.log(\"session.kernel.name:\", session.kernel ? session.kernel.name : \"no kernel\");\n", "                        console.log(\"session.kernel.requestExecute:\", session.kernel ? typeof session.kernel.requestExecute : \"no kernel\");\n", "                        \n", "                        if (session.kernel && session.kernel.requestExecute) {\n", "                            console.log(\"✅ KERNEL FOUND AND READY!\");\n", "                            \n", "                            // Test execution\n", "                            console.log(\"Testing kernel execution...\");\n", "                            try {\n", "                                const future = session.kernel.requestExecute({\n", "                                    code: \"write(\\'🎉 JupyterLab kernel test successful!\\'), nl.\"\n", "                                });\n", "                                console.log(\"✅ Execution successful, future:\", future);\n", "                            } catch (e) {\n", "                                console.error(\"❌ Execution failed:\", e);\n", "                            }\n", "                        } else {\n", "                            console.log(\"❌ Kernel or requestExecute not available\");\n", "                        }\n", "                    } else {\n", "                        console.log(\"❌ No session available\");\n", "                    }\n", "                } else {\n", "                    console.log(\"❌ No sessionContext available\");\n", "                }\n", "            } else {\n", "                console.log(\"❌ No context available\");\n", "            }\n", "        } else {\n", "            console.log(\"❌ No currentWidget available\");\n", "        }\n", "    } else {\n", "        console.log(\"❌ No shell available\");\n", "    }\n", "} else {\n", "    console.log(\"❌ JupyterLab app not available\");\n", "    console.log(\"Checking for classic Ju<PERSON>ter...\");\n", "    console.log(\"Jupyter:\", typeof Jupyter);\n", "    console.log(\"IPython:\", typeof IPython);\n", "}\n", "\n", "console.log(\"=== END DEBUG ===\");\n", "</script>'\n", "])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Test Widget Creation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::create_text_input(debug_widget, 'Debug Test:', 'Test Value')."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Manual Widget Update Test\n", "\n", "This will create a manual test function you can call from the browser console:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["% Create manual test function\n", "jupyter_response_handling::assert_success_response(manual_test, [], '', [\n", "    widget_html-'<script>\n", "window.testJupyterLabWidget = function() {\n", "    console.log(\"Manual widget test starting...\");\n", "    \n", "    if (typeof window.jupyterapp !== \"undefined\" && window.jupyterapp.shell) {\n", "        const widget = window.jupyterapp.shell.currentWidget;\n", "        if (widget && widget.context && widget.context.sessionContext && widget.context.sessionContext.session) {\n", "            const kernel = widget.context.sessionContext.session.kernel;\n", "            if (kernel && kernel.requestExecute) {\n", "                console.log(\"Executing manual widget update...\");\n", "                const testCode = \"jupyter_widget_handling::set_widget_value(debug_widget, \\'manual_update_success\\').\"; \n", "                const future = kernel.requestExecute({ code: testCode });\n", "                console.log(\"Manual test executed, future:\", future);\n", "                return true;\n", "            }\n", "        }\n", "    }\n", "    console.log(\"Manual test failed - kernel not available\");\n", "    return false;\n", "};\n", "\n", "console.log(\"Manual test function available: window.testJupyterLabWidget()\");\n", "</script>'\n", "])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Check Widget Value"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::get_widget_value(debug_widget, Value),\n", "format('Current widget value: ~w~n', [Value])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Instructions\n", "\n", "1. **Run all cells above**\n", "2. **Open browser console** (F12)\n", "3. **Look for the detailed debug output**\n", "4. **If you see \"✅ KERNEL FOUND AND READY!\"**, the automatic detection should work\n", "5. **If not**, try the manual test by typing in console: `window.testJupyterLabWidget()`\n", "6. **Then run Step 4** to see if the value updated\n", "\n", "## Expected Results\n", "\n", "- You should see detailed information about what's available in JupyterLab\n", "- If the kernel is found, you should see a success message in the notebook output\n", "- The manual test should return `true` if successful\n", "- The widget value should update when you interact with it or run the manual test"]}], "metadata": {"kernelspec": {"display_name": "Logtalk", "language": "logtalk", "name": "logtalk_kernel"}, "language_info": {"codemirror_mode": "logtalk", "file_extension": ".lgt", "mimetype": "text/x-logtalk", "name": "Logtalk"}}, "nbformat": 4, "nbformat_minor": 4}