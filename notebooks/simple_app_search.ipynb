{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Simple JupyterLab App Search\n", "\n", "A safer approach to find the JupyterLab application without variable conflicts."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Create Test Widget"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::create_text_input(simple_app_test, 'Simple App Test:', 'initial')."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Safe Application Search\n", "\n", "Run these **one at a time** in browser console (F12):\n", "\n", "### Test A: Check for shell objects\n", "```javascript\n", "(function() {\n", "    console.log('=== Test A: Shell Objects ===');\n", "    const keys = Object.keys(window);\n", "    for (const key of keys) {\n", "        try {\n", "            if (window[key] && window[key].shell) {\n", "                console.log('Found shell in:', key, window[key]);\n", "            }\n", "        } catch (e) {}\n", "    }\n", "})();\n", "```\n", "\n", "### Test B: Check document body\n", "```javascript\n", "(function() {\n", "    console.log('=== Test B: Document Body ===');\n", "    const body = document.body;\n", "    for (const key in body) {\n", "        if (key.includes('jupyter') || key.includes('lab')) {\n", "            console.log('Found in body:', key, body[key]);\n", "        }\n", "    }\n", "})();\n", "```\n", "\n", "### Test C: Check plugins for app reference\n", "```javascript\n", "(function() {\n", "    console.log('=== Test C: Plugin Check ===');\n", "    const plugins = window._JUPYTERLAB;\n", "    for (const name in plugins) {\n", "        const plugin = plugins[name];\n", "        if (plugin && typeof plugin === 'object') {\n", "            for (const key in plugin) {\n", "                if (key.includes('app') || key.includes('shell')) {\n", "                    console.log(`Plugin ${name}.${key}:`, plugin[key]);\n", "                }\n", "            }\n", "        }\n", "    }\n", "})();\n", "```\n", "\n", "### Test D: Check for current widget directly\n", "```javascript\n", "(function() {\n", "    console.log('=== Test D: Widget Search ===');\n", "    const keys = Object.keys(window);\n", "    for (const key of keys) {\n", "        try {\n", "            const obj = window[key];\n", "            if (obj && obj.currentWidget) {\n", "                console.log('Found currentWidget in:', key, obj);\n", "            }\n", "        } catch (e) {}\n", "    }\n", "})();\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Test Widget Update\n", "\n", "After running the tests above, try this:\n", "\n", "```javascript\n", "(function() {\n", "    console.log('=== Widget Update Test ===');\n", "    if (typeof updateLogtalkWidget === 'function') {\n", "        updateLogtalkWidget('simple_app_test', 'console_update_test');\n", "        console.log('Update function called');\n", "    } else {\n", "        console.log('updateLogtalkWidget not available');\n", "    }\n", "})();\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Check Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::get_widget_value(simple_app_test, Value),\n", "format('Current value: ~w~n', [Value])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Manual <PERSON>\n", "\n", "If any of the tests above found an object with shell/currentWidget, try this manual test:\n", "\n", "```javascript\n", "// Replace 'FOUND_OBJECT' with whatever was found in the tests above\n", "(function() {\n", "    console.log('=== Manual Kernel Test ===');\n", "    \n", "    // Example: if you found window.someApp with shell\n", "    // const app = window.someApp;\n", "    \n", "    // Try this pattern with whatever object was found:\n", "    // if (app && app.shell && app.shell.currentWidget) {\n", "    //     const widget = app.shell.currentWidget;\n", "    //     if (widget.context && widget.context.sessionContext) {\n", "    //         const kernel = widget.context.sessionContext.session.kernel;\n", "    //         if (kernel && kernel.requestExecute) {\n", "    //             kernel.requestExecute({\n", "    //                 code: \"jupyter_widget_handling::set_widget_value('simple_app_test', 'manual_kernel_success').\"\n", "    //             });\n", "    //             console.log('Manual kernel execution attempted');\n", "    //         }\n", "    //     }\n", "    // }\n", "    \n", "    console.log('Replace FOUND_OBJECT with actual discovered object');\n", "})();\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Instructions\n", "\n", "1. **Run each test (A, B, C, D) separately** in browser console\n", "2. **Look for any objects** that have `shell`, `currentWidget`, or app-like properties\n", "3. **Try the widget update test**\n", "4. **If you find a promising object**, modify and run the manual kernel test\n", "5. **Check Step 4** to see if values updated\n", "\n", "The separate function wrappers prevent variable conflicts."]}], "metadata": {"kernelspec": {"display_name": "Logtalk", "language": "logtalk", "name": "logtalk"}, "language_info": {"codemirror_mode": "prolog", "file_extension": ".lgt", "mimetype": "text/x-logtalk", "name": "logtalk", "pygments_lexer": "prolog", "version": "3.0"}}, "nbformat": 4, "nbformat_minor": 4}