{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Real-Time Widget Test\n", "\n", "Testing widgets that automatically sync as you type (like real widgets should!)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Create Real-Time Widget"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::create_text_input(realtime_test, 'Real-Time Widget:', 'start_typing')."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Check Initial Value"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::get_widget_value(realtime_test, Value),\n", "format('Initial value: ~w~n', [Value])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Test Real-Time Updates\n", "\n", "**Start typing in the widget above.** Watch the status indicator:\n", "\n", "- **\"typing...\"** (gray) - You're currently typing\n", "- **\"✓ synced\"** (green) - Value automatically synced to server\n", "- **\"⚠ manual sync needed\"** (orange) - Auto-sync failed, fallback to localStorage\n", "\n", "The widget should automatically sync **500ms after you stop typing**."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Check Real-Time Updates\n", "\n", "**After typing and seeing \"✓ synced\", run this cell:**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::get_widget_value(realtime_test, Value),\n", "format('Current value: ~w~n', [Value])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Interactive Demo\n", "\n", "Let's create multiple widgets for a real interactive experience:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::create_text_input(name_field, 'Your Name:', ''),\n", "jupyter::create_text_input(city_field, 'Your City:', ''),\n", "jupyter::create_text_input(hobby_field, 'Your Ho<PERSON>:', '')."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Fill Out the Form\n", "\n", "**Fill in the three fields above.** Each should show \"✓ synced\" as you finish typing in each field."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 7: Live Results\n", "\n", "**Run this cell after filling out the form to see live results:**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::get_widget_value(name_field, Name),\n", "jupyter::get_widget_value(city_field, City),\n", "jupyter::get_widget_value(hobby_field, Hobby),\n", "format('=== LIVE FORM RESULTS ===~n', []),\n", "format('Hello ~w from ~w!~n', [Name, City]),\n", "format('I see you enjoy ~w.~n', [<PERSON><PERSON>]),\n", "format('========================~n', [])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 8: Continuous Updates\n", "\n", "**Try this:** Change any of the values in the widgets above, then re-run Step 7. The results should update immediately without any manual sync commands!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## How This Works\n", "\n", "This approach uses:\n", "\n", "1. **`oninput` events** - Triggers as you type\n", "2. **Debounced updates** - Waits 500ms after you stop typing\n", "3. **Direct kernel execution** - Uses `kernel.execute()` if available\n", "4. **Visual feedback** - Status shows sync state\n", "5. **Graceful fallback** - localStorage if kernel not found\n", "\n", "## Expected Results\n", "\n", "### If Real-Time Sync Works:\n", "- ✅ Status shows \"✓ synced\" after typing\n", "- ✅ `get_widget_value` returns current typed values\n", "- ✅ No manual commands needed\n", "- ✅ True interactive widget experience\n", "\n", "### If Fallback Mode:\n", "- ⚠️ Status shows \"⚠ manual sync needed\"\n", "- 📦 Values stored in localStorage\n", "- 🔧 Need manual sync commands\n", "\n", "## <PERSON><PERSON><PERSON> Console\n", "\n", "Check browser console (F12) for:\n", "- \"Auto-updating widget realtime_test with value: your_value\"\n", "- \"Successfully executed: jupyter_widget_handling::set_widget_value(...)\"\n", "- \"No kernel found, stored in localStorage\" (if fallback)\n", "\n", "## This Is What Widgets Should Be!\n", "\n", "If this works, you'll have **true interactive widgets** that:\n", "- Update automatically as you type\n", "- Provide immediate feedback\n", "- Work like widgets in any modern application\n", "- Require no manual code execution\n", "\n", "**This is the real widget experience we've been working toward!**"]}], "metadata": {"kernelspec": {"display_name": "Logtalk", "language": "logtalk", "name": "logtalk"}, "language_info": {"codemirror_mode": "prolog", "file_extension": ".lgt", "mimetype": "text/x-logtalk", "name": "logtalk", "version": "3.0"}}, "nbformat": 4, "nbformat_minor": 4}