{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["lvm."]}, {"cell_type": "code", "execution_count": 2, "id": "c1e0a525", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["catch(';'(('->'(fail,true)),';'(('->'(fail,true)),true)), Error, true)."]}, {"cell_type": "code", "execution_count": 3, "id": "23c23adc", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mVD = lvm(6,5,0,bd1181bed)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["current_prolog_flag(version_data, VD)."]}, {"cell_type": "code", "execution_count": 4, "id": "ad025acf", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["once(';'(('->'(fail,true)),';'(('->'(fail,true)),true)))."]}, {"cell_type": "code", "execution_count": 5, "id": "9bf9f677", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["call(';'('->'(fail,true),true))."]}, {"cell_type": "code", "execution_count": 6, "id": "82dc00bd", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["call(';'(('->'(fail,true)),';'(('->'(fail,true)),true)))."]}, {"cell_type": "code", "execution_count": 7, "id": "1f6ba70d", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["(fail -> true; fail -> true; true)."]}, {"cell_type": "code", "execution_count": null, "id": "dde0c039", "metadata": {"vscode": {"languageId": "logtalk"}}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Logtalk", "language": "logtalk", "name": "logtalk_kernel"}, "language_info": {"codemirror_mode": "logtalk", "file_extension": ".lgt", "mimetype": "text/x-logtalk", "name": "Logtalk"}}, "nbformat": 4, "nbformat_minor": 5}