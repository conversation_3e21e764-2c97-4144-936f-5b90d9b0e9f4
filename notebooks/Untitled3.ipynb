{"cells": [{"cell_type": "code", "execution_count": 1, "id": "45dc5433-d2f7-4da9-9fce-c0eaa453c2bd", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["lvm."]}, {"cell_type": "code", "execution_count": 2, "id": "266c233e-aaed-4f2a-a639-df36a6ed2827", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1mtrue"]}, "metadata": {}, "output_type": "display_data"}], "source": ["eclipse."]}, {"cell_type": "code", "execution_count": 3, "id": "683a2104-2d0b-4392-8a6a-9babcb26e89b", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1mYes"]}, "metadata": {}, "output_type": "display_data"}], "source": ["gnu."]}, {"cell_type": "code", "execution_count": 4, "id": "9e4488aa-34c3-4916-ad36-a64d944566d4", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1mVD = gprolog(1,5,1,[])"]}, "metadata": {}, "output_type": "display_data"}], "source": ["current_prolog_flag(version_data, VD)."]}, {"cell_type": "code", "execution_count": 5, "id": "24eb1d33-332f-4089-87ac-6a92ed966f47", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1myes"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sicstus."]}, {"cell_type": "code", "execution_count": 6, "id": "37d7c732-7053-4b32-8323-6aec46b655ea", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1mVD = sicstus(4,8,0,3,[])"]}, "metadata": {}, "output_type": "display_data"}], "source": ["current_prolog_flag(version_data, VD)."]}, {"cell_type": "code", "execution_count": 7, "id": "2c2bc991-1379-4130-bea5-d6bdd3246b79", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1myes"]}, "metadata": {}, "output_type": "display_data"}], "source": ["lvm."]}, {"cell_type": "code", "execution_count": 8, "id": "d17c56f5-7cbe-4746-b1ef-95be1b49e3ea", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[1mE = UTF-8"]}, "metadata": {}, "output_type": "display_data"}], "source": ["current_prolog_flag(encoding, E)."]}, {"cell_type": "code", "execution_count": null, "id": "438a3b80-d6ab-4be7-8ffa-4af7411b1167", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Logtalk", "language": "logtalk", "name": "logtalk_kernel"}, "language_info": {"codemirror_mode": "logtalk", "file_extension": ".lgt", "mimetype": "text/x-logtalk", "name": "Logtalk"}}, "nbformat": 4, "nbformat_minor": 5}