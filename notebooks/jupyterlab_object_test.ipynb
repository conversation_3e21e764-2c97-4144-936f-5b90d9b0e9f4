{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# JupyterLab Object Test\n", "\n", "Testing the _JUPYTERLAB global object approach."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Create Test Widget"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::create_text_input(lab_object_test, 'JupyterLab Object Test:', 'initial_value')."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> Test\n", "\n", "Run this in browser console (F12):\n", "\n", "```javascript\n", "// Detailed _JUPYTERLAB exploration\n", "console.log('=== _JUPYTERLAB Object Exploration ===');\n", "console.log('_JUPYTERLAB:', window._JUPYTERLAB);\n", "console.log('_JUPYTERLAB keys:', Object.keys(window._JUPYTERLAB || {}));\n", "\n", "if (window._JUPYTERLAB) {\n", "    const lab = window._JUPYTERLAB;\n", "    \n", "    console.log('lab.shell:', lab.shell);\n", "    if (lab.shell) {\n", "        console.log('shell.currentWidget:', lab.shell.currentWidget);\n", "        \n", "        if (lab.shell.currentWidget) {\n", "            const widget = lab.shell.currentWidget;\n", "            console.log('widget.context:', widget.context);\n", "            \n", "            if (widget.context) {\n", "                console.log('context.sessionContext:', widget.context.sessionContext);\n", "                \n", "                if (widget.context.sessionContext) {\n", "                    console.log('sessionContext.session:', widget.context.sessionContext.session);\n", "                    \n", "                    if (widget.context.sessionContext.session) {\n", "                        const session = widget.context.sessionContext.session;\n", "                        console.log('session.kernel:', session.kernel);\n", "                        console.log('kernel.requestExecute:', session.kernel ? typeof session.kernel.requestExecute : 'no kernel');\n", "                        \n", "                        if (session.kernel && session.kernel.requestExecute) {\n", "                            console.log('✅ KERNEL FOUND VIA _JUPYTERLAB!');\n", "                            \n", "                            // Test execution\n", "                            try {\n", "                                const future = session.kernel.requestExecute({\n", "                                    code: \"write('🎉 _JUPYTERLAB kernel test successful!'), nl.\"\n", "                                });\n", "                                console.log('✅ Test execution successful:', future);\n", "                            } catch (e) {\n", "                                console.error('❌ Test execution failed:', e);\n", "                            }\n", "                        }\n", "                    }\n", "                }\n", "            }\n", "        }\n", "    }\n", "    \n", "    // Check serviceManager\n", "    console.log('lab.serviceManager:', lab.serviceManager);\n", "    if (lab.serviceManager && lab.serviceManager.sessions) {\n", "        console.log('serviceManager.sessions:', lab.serviceManager.sessions);\n", "        if (lab.serviceManager.sessions.running) {\n", "            const sessions = lab.serviceManager.sessions.running();\n", "            console.log('Running sessions:', sessions);\n", "            if (sessions.length > 0) {\n", "                const session = sessions[0];\n", "                console.log('First session:', session);\n", "                console.log('Session kernel:', session.kernel);\n", "            }\n", "        }\n", "    }\n", "}\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Test Widget Update\n", "\n", "After running the exploration above, test the widget update:\n", "\n", "```javascript\n", "// Test the enhanced widget update\n", "console.log('=== Widget Update Test ===');\n", "if (typeof updateLogtalkWidget === 'function') {\n", "    updateLogtalkWidget('lab_object_test', 'jupyterlab_object_success');\n", "    console.log('Widget update function called');\n", "} else {\n", "    console.log('updateLogtalkWidget function not available');\n", "}\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Check Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::get_widget_value(lab_object_test, Value),\n", "format('Final value: ~w~n', [Value])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Manual Test (if needed)\n", "\n", "If the automatic method doesn't work, try this manual approach in console:\n", "\n", "```javascript\n", "// Manual kernel execution test\n", "if (window._JUPYTERLAB && window._JUPYTERLAB.shell && window._JUPYTERLAB.shell.currentWidget) {\n", "    const widget = window._JUPYTERLAB.shell.currentWidget;\n", "    if (widget.context && widget.context.sessionContext && widget.context.sessionContext.session) {\n", "        const kernel = widget.context.sessionContext.session.kernel;\n", "        if (kernel && kernel.requestExecute) {\n", "            // Manual widget update\n", "            const code = \"jupyter_widget_handling::set_widget_value('lab_object_test', 'manual_success').\";\n", "            kernel.requestExecute({ code: code });\n", "            console.log('Manual update sent');\n", "        }\n", "    }\n", "}\n", "```"]}], "metadata": {"kernelspec": {"display_name": "Logtalk", "language": "logtalk", "name": "logtalk"}, "language_info": {"codemirror_mode": "prolog", "file_extension": ".lgt", "mimetype": "text/x-logtalk", "name": "logtalk", "pygments_lexer": "prolog", "version": "3.0"}}, "nbformat": 4, "nbformat_minor": 4}