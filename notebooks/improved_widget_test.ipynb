{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Improved Widget Test\n", "\n", "Safe widget system with helpful alerts that show the exact sync command."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Create Widget"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::create_text_input(improved_test, 'Improved Widget:', 'initial_value')."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Check Initial Value"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::get_widget_value(improved_test, Value),\n", "format('Current value: ~w~n', [Value])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Use the Widget\n", "\n", "1. **Type something new** in the text input above (e.g., \"my_new_value\")\n", "2. **Click the \"Update\" button**\n", "3. **The alert will show the exact sync command** to copy and paste\n", "\n", "The alert will look like:\n", "```\n", "Value captured: my_new_value. Run: jupyter::sync_widget(improved_test, 'my_new_value')\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: <PERSON><PERSON> and Run the Sync Command\n", "\n", "**Copy the command from the alert and paste it below:**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["% Paste the sync command from the alert here\n", "% Example: jupyter::sync_widget(improved_test, 'my_new_value').\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Verify the Update"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::get_widget_value(improved_test, Value),\n", "format('Updated value: ~w~n', [Value])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Multiple Widgets Example\n", "\n", "Let's create several widgets to test the workflow:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::create_text_input(name_widget, 'Your Name:', 'Anonymous'),\n", "jupyter::create_text_input(email_widget, 'Your Email:', '<EMAIL>'),\n", "jupyter::create_text_input(comment_widget, 'Comments:', 'No comments')."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fill Out the Form\n", "\n", "1. **Fill in all three widgets** above with your information\n", "2. **Click \"Update\" on each widget** to capture the values\n", "3. **Copy and run the sync commands** from the alerts\n", "4. **Check the results** below"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["% Paste sync commands here:\n", "% jupyter::sync_widget(name_widget, 'your_name').\n", "% jupyter::sync_widget(email_widget, 'your_email').\n", "% jupyter::sync_widget(comment_widget, 'your_comment').\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Display Form Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["jupyter::get_widget_value(name_widget, Name),\n", "jupyter::get_widget_value(email_widget, Email),\n", "jupyter::get_widget_value(comment_widget, Comment),\n", "format('=== FORM RESULTS ===~n', []),\n", "format('Name: ~w~n', [Name]),\n", "format('Email: ~w~n', [Email]),\n", "format('Comment: ~w~n', [Comment]),\n", "format('==================~n', [])."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Benefits of This Approach\n", "\n", "- ✅ **Kernel-safe** - no complex JavaScript that breaks JSON\n", "- ✅ **Copy-paste friendly** - exact sync commands in alerts\n", "- ✅ **No manual typing** - just copy from alert and paste\n", "- ✅ **Clear workflow** - capture, copy, paste, verify\n", "- ✅ **Multiple widgets** - works with forms and complex UIs\n", "- ✅ **Reliable** - localStorage backup always available\n", "\n", "## B<PERSON>er Console Check\n", "\n", "You can also check captured values in browser console (F12):\n", "\n", "```javascript\n", "// Check all captured widget values\n", "for (let i = 0; i < localStorage.length; i++) {\n", "    const key = localStorage.key(i);\n", "    if (key.startsWith('widget_')) {\n", "        console.log(key + ':', localStorage.getItem(key));\n", "    }\n", "}\n", "```\n", "\n", "This improved approach provides a smooth workflow while staying kernel-safe!"]}], "metadata": {"kernelspec": {"display_name": "Logtalk", "language": "logtalk", "name": "logtalk"}, "language_info": {"codemirror_mode": "prolog", "file_extension": ".lgt", "mimetype": "text/x-logtalk", "name": "logtalk", "pygments_lexer": "prolog", "version": "3.0"}}, "nbformat": 4, "nbformat_minor": 4}